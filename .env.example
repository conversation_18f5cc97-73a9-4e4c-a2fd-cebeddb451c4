# OpenAI API Configuration
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-4.1-mini

# Cohere API Configuration
COHERE_API_KEY=your-cohere-api-key

# Application Security
SESSION_SECRET_KEY=your-session-secret-key
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your-admin-password
GUEST_USERNAME=guest
GUEST_PASSWORD=your-guest-password

# Server Configuration
HOST=0.0.0.0
PORT=8082
CORS_ORIGINS=http://localhost:8082

# Storage Configuration
# Set USE_SHAREPOINT=true to use SharePoint or USE_GCS=true to use Google Cloud Storage
USE_SHAREPOINT=true
USE_GCS=false

# SharePoint Configuration (required if USE_SHAREPOINT=true)
MS_CLIENT_ID=your-azure-app-client-id
MS_CLIENT_SECRET=your-azure-app-client-secret
MS_TENANT_ID=your-azure-tenant-id
SHAREPOINT_SITE_NAME=your-sharepoint-site-name
SHAREPOINT_LIBRARY_NAME=Documents

# Google Cloud Storage Configuration (required if USE_GCS=true)
GCS_BUCKET=your-gcs-bucket-name
GCS_PREFIX=ddb-rag
# Either set GOOGLE_APPLICATION_CREDENTIALS environment variable to point to your credentials file
# or provide the credentials JSON directly in GCS_CREDENTIALS_JSON
GCS_CREDENTIALS_JSON=your-base64-encoded-credentials-json

# File Settings
FILE_SIZE_LIMIT=104857600  # 100MB in bytes

# Model Settings
CHUNK_SIZE=512
CHUNK_OVERLAP=50
SIMILARITY_TOP_K=20

# Paths (optional, defaults to current directory)
BASE_DIR=.
DATA_DIR=data
STORAGE_DIR=storage

# Google Cloud Storage Settings (optional, for cloud storage)
USE_GCS=false
GCS_BUCKET=your-bucket-name
GCS_PREFIX=ddb-rag
GOOGLE_APPLICATION_CREDENTIALS=path/to/your/google-credentials.json

# S3 Settings (optional, for cloud storage)
USE_S3=false
S3_BUCKET=your-bucket-name
S3_PREFIX=ddb-rag
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_REGION=us-east-1

# CORS Settings (optional, defaults to localhost and render.com)
CORS_ORIGINS=["http://localhost:8082","http://0.0.0.0:8082","https://*.onrender.com"]

# Application Configuration
PORT=8082
HOST=0.0.0.0 