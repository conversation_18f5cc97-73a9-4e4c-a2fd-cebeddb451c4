import os
from pathlib import Path
from typing import Optional, List, Union, Dict, Any

try:
    from pydantic_settings import BaseSettings, SettingsConfigDict
except ImportError:
    from pydantic import BaseSettings

    # Fallback SettingsConfigDict for pydantic v1
    SettingsConfigDict = dict
from pydantic import SecretStr, Field, validator
import json
import logging
import tempfile
import base64

logger = logging.getLogger(__name__)


class Settings(BaseSettings):
    """Application settings."""

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        extra="ignore",
        case_sensitive=True,
        validate_default=False,  # Allow None values for optional fields
        use_enum_values=True,  # Use enum values instead of enum objects
    )

    # API Keys
    OPENAI_API_KEY: SecretStr = Field(..., env="OPENAI_API_KEY")
    COHERE_API_KEY: SecretStr = Field(..., env="COHERE_API_KEY")
    SESSION_SECRET_KEY: SecretStr = Field(
        default_factory=lambda: SecretStr(os.urandom(32).hex())
    )

    # Security Settings
    USERNAME: str = Field(default="admin", env="USERNAME")
    PASSWORD: SecretStr = Field(
        default_factory=lambda: SecretStr(os.urandom(16).hex()), env="PASSWORD"
    )
    ADMIN_EMAILS: str = Field(
        default="",
        env="ADMIN_EMAILS",  # Comma-separated list of admin emails
    )

    # Server Settings
    HOST: str = Field(default="0.0.0.0", env="HOST")
    PORT: int = Field(default=8082, env="PORT")
    CORS_ORIGINS: str = Field(default="*", env="CORS_ORIGINS")

    # SharePoint Configuration
    USE_SHAREPOINT: bool = Field(default=False, env="USE_SHAREPOINT")
    MS_CLIENT_ID: str | None = Field(default=None, env="MS_CLIENT_ID")
    MS_CLIENT_SECRET: SecretStr | None = Field(default=None, env="MS_CLIENT_SECRET")
    MS_TENANT_ID: str | None = Field(default=None, env="MS_TENANT_ID")
    SHAREPOINT_SITE_NAME: str | None = Field(default=None, env="SHAREPOINT_SITE_NAME")
    SHAREPOINT_LIBRARY_NAME: str | None = Field(
        default=None, env="SHAREPOINT_LIBRARY_NAME"
    )

    # Model Settings
    OPENAI_MODEL: str = Field(default="gpt-4.1-mini", env="OPENAI_MODEL")
    CHUNK_SIZE: int = Field(default=512, env="CHUNK_SIZE")
    CHUNK_OVERLAP: int = Field(default=100, env="CHUNK_OVERLAP")
    SIMILARITY_TOP_K: int = Field(default=20, env="SIMILARITY_TOP_K")

    # Storage Configuration (Now only local)
    STORAGE_DIR: Path = Field(default=Path("storage"))
    DATA_DIR: Path = Field(default=Path("storage/data"))  # Keep local data dir
    UPLOAD_DIR: Path = Field(
        default=Path("storage/uploads")
    )  # Keep local upload dir for temp
    TEMP_DIR: Path = Field(
        default=Path("storage/temp")
    )  # Add temp directory for SharePoint imports

    # File Handling
    ALLOWED_EXTENSIONS: List[str] = Field(
        default=["pdf", "docx", "pptx", "txt", "md", "png", "jpg", "jpeg", "gif", "bmp"]
    )
    FILE_SIZE_LIMIT: int = Field(
        default=50 * 1024 * 1024,
        env="FILE_SIZE_LIMIT",  # 50MB limit
    )

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        logger.info("Creating settings instance...")

        # Initialize storage backend
        if self.USE_SHAREPOINT:
            self._validate_sharepoint_config()

    def _validate_sharepoint_config(self):
        """Validate SharePoint configuration."""
        logger.info("Validating SharePoint configuration...")
        required_fields = {
            "MS_CLIENT_ID": self.MS_CLIENT_ID,
            "MS_CLIENT_SECRET": self.MS_CLIENT_SECRET,
            "MS_TENANT_ID": self.MS_TENANT_ID,
        }

        missing_fields = [
            field for field, value in required_fields.items() if not value
        ]
        if missing_fields:
            error_msg = f"Missing required SharePoint configuration: {', '.join(missing_fields)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        logger.info("SharePoint configuration validated successfully")

    @validator(
        "MS_CLIENT_ID",
        "MS_CLIENT_SECRET",
        "MS_TENANT_ID",
        "SHAREPOINT_SITE_NAME",
        "SHAREPOINT_LIBRARY_NAME",
        pre=True,
        always=True,
    )
    def check_sharepoint_fields(cls, v, values):
        if values.get("USE_SHAREPOINT") and not v:
            raise ValueError(
                f"{', '.join(values.keys())} must be set if USE_SHAREPOINT is true"
            )
        return v

    @property
    def cors_origin_list(self) -> List[str]:
        """Get list of allowed CORS origins."""
        return [origin.strip() for origin in self.CORS_ORIGINS.split(",")]

    def get_non_secret_config(self) -> Dict[str, Any]:
        # ... (implementation, ensure no GCS vars are included) ...
        return {
            # ... include other non-secrets ...
            "USE_SHAREPOINT": self.USE_SHAREPOINT,
            "SHAREPOINT_SITE_NAME": self.SHAREPOINT_SITE_NAME,
            "SHAREPOINT_LIBRARY_NAME": self.SHAREPOINT_LIBRARY_NAME,
            "OPENAI_MODEL": self.OPENAI_MODEL,
            "CHUNK_SIZE": self.CHUNK_SIZE,
            "CHUNK_OVERLAP": self.CHUNK_OVERLAP,
            "SIMILARITY_TOP_K": self.SIMILARITY_TOP_K,
            "STORAGE_DIR": str(self.STORAGE_DIR),
            "DATA_DIR": str(self.DATA_DIR),
            "UPLOAD_DIR": str(self.UPLOAD_DIR),
            "TEMP_DIR": str(self.TEMP_DIR),
            "ALLOWED_EXTENSIONS": self.ALLOWED_EXTENSIONS,
            "FILE_SIZE_LIMIT": self.FILE_SIZE_LIMIT,
            "CORS_ORIGINS": self.CORS_ORIGINS,
        }


# Create global settings instance
logger.info("Creating settings instance...")
settings = Settings()
logger.info(
    f"Settings configured: USE_SHAREPOINT={settings.USE_SHAREPOINT}, SHAREPOINT_SITE_NAME={settings.SHAREPOINT_SITE_NAME}"
)

# No need to create local directories when using SharePoint
