{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('list_sharepoint_sites') }}">Sites</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('list_sharepoint_drives', site_id=site_id) }}">Document Libraries</a></li>
            <li class="breadcrumb-item active" aria-current="page">Files</li>
        </ol>
    </nav>

    <h2 class="mb-4">Browse Files</h2>
    
    {% if current_path %}
    <nav aria-label="folder navigation" class="mb-3">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('list_sharepoint_files', drive_id=drive_id) }}">Root</a></li>
            {% for part in current_path.split('/') %}
                {% if part %}
                    {% set path = current_path.split(part)[0] + part %}
                    {% set base_url = url_for('list_sharepoint_files', drive_id=drive_id) %}
                    {% set full_url = base_url ~ '?folder_path=' ~ (path | urlencode) %}
                    <li class="breadcrumb-item">
                        <a href="{{ full_url }}">{{ part }}</a>
                    </li>
                {% endif %}
            {% endfor %}
        </ol>
    </nav>
    {% endif %}
    
    <div class="list-group">
        {% for item in files %}
            {% if item.folder %}
                {% set folder_link_path = item.parentReference.path.replace('/drives/' + drive_id + '/root:', '') + '/' + item.name %}
                {% set folder_base_url = url_for('list_sharepoint_files', drive_id=drive_id) %}
                {% set folder_full_url = folder_base_url ~ '?folder_path=' ~ (folder_link_path | urlencode) %}
                <a href="{{ folder_full_url }}" class="list-group-item list-group-item-action">
                    <div class="d-flex w-100 justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-folder text-warning me-2"></i>
                            <span>{{ item.name }}</span>
                        </div>
                        <small class="text-muted">{{ item.folder.childCount }} items</small>
                    </div>
                </a>
            {% else %}
            <div class="list-group-item">
                <div class="d-flex w-100 justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-file text-primary me-2"></i>
                        <span>{{ item.name }}</span>
                    </div>
                    <div>
                        <small class="text-muted me-3">{{ item.size | filesizeformat }}</small>
                        <a href="{{ url_for('import_sharepoint_item_route', drive_id=drive_id, item_id=item.id, item_name=item.name) }}" 
                           class="btn btn-sm btn-primary">
                            Import
                        </a>
                    </div>
                </div>
            </div>
            {% endif %}
        {% else %}
        <div class="alert alert-info">
            This folder is empty.
        </div>
        {% endfor %}
    </div>
</div>

{% block extra_js %}
<script src="https://kit.fontawesome.com/your-font-awesome-kit.js" crossorigin="anonymous"></script>
{% endblock %}
{% endblock %} 