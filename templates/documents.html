{% extends "base.html" %}

{% block title %}Manage Documents{% endblock %}

{% block content %}
<div class="container mt-5">
    <h2>Manage Documents</h2>
    <hr>

    <div class="mb-4">
        <h4>Import from SharePoint</h4>
        <p>Browse your SharePoint sites to import documents into the RAG system.</p>
        <a href="{{ request.url_for('list_sharepoint_sites') }}" class="btn btn-primary">
            <i class="fas fa-cloud-upload-alt me-2"></i> Browse SharePoint
        </a>
        {# Add local file upload option if needed in the future #}
        {#
        <h4 class="mt-4">Upload Local File</h4>
        <form action="/upload" method="post" enctype="multipart/form-data" class="mt-3">
            <div class="input-group">
                <input type="file" class="form-control" name="file" id="fileInput" required>
                <button class="btn btn-secondary" type="submit">Upload & Index</button>
            </div>
        </form>
        #}
    </div>

    <hr>

    <h4>Indexed Documents</h4>
    {% if indexed_files %}
        <p>The following documents are currently indexed and available for querying:</p>
        <ul class="list-group">
            {% for file_name in indexed_files %}
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    <span>
                        <i class="fas fa-file-alt me-2"></i> 
                        {{ file_name }}
                        {# <small class="text-muted ms-2">(ID: {{ doc_id }})</small> #} {# Removed doc_id as we only have filename #}
                    </span>
                     {# Add size/date if needed later by querying metadata again #}
                     {# Add delete button if needed later - would need filename-based deletion #}
                </li>
            {% endfor %}
        </ul>
    {% else %}
        <div class="alert alert-info" role="alert">
            No documents are currently indexed. Use the button above to import from SharePoint.
        </div>
    {% endif %}

</div>

{# Add FontAwesome if not already included in base.html #}
{# <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script> #}

{# Add JS for delete functionality if implemented later #}
{#
<script>
function deleteDocument(docId) {
    if (confirm('Are you sure you want to remove this document from the index?')) {
        fetch(`/documents/${docId}`, { method: 'DELETE' })
            .then(response => {
                if (response.ok) {
                    window.location.reload(); // Reload to show updated list
                } else {
                    alert('Error deleting document.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error deleting document.');
            });
    }
}
</script>
#}

{% endblock %} 