# Gemini Project Information

This file provides context for the Gemini agent to understand and interact with this project.

## Project Overview

This is a Retrieval-Augmented Generation (RAG) application designed to interact with documents stored in SharePoint. It provides a web-based chat interface for users to ask questions about the content of their documents.

## Key Technologies

*   **Backend:** FastAPI
*   **Frontend:** Jinja2 Templates
*   **RAG Framework:** LlamaIndex
*   **LLMs:** OpenAI, Cohere
*   **SharePoint Integration:** Microsoft Graph API (via `msal` and `aiohttp`)
*   **Authentication:** Basic Auth, Microsoft OAuth2
*   **Database/Storage:** Local file system for vector store and uploaded documents.

## How to Run the Application

1.  **Install dependencies:**
    ```bash
    pip install -r requirements.txt
    ```
2.  **Configure environment variables:**
    Create a `.env` file and populate it with the necessary credentials (see `.env.example`).
3.  **Run the application:**
    ```bash
    uvicorn app:app --host localhost --port 8082 --reload
    ```

## How to Run Tests

There are no dedicated test files in the project. The application can be tested manually by running it and interacting with the web interface.
