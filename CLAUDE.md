# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Development Commands

### Running the Application
- **Development**: `uvicorn app:app --host localhost --port 8082 --reload --reload-exclude="storage/*" --reload-exclude="*.log" --reload-exclude="temp/*"`
- **Production**: `uvicorn app:app --host 0.0.0.0 --port 8082`

### Testing
- **Run tests**: `python -m pytest` (if pytest.ini exists) or `python run_tests.py`
- **Individual test**: `python test_auth_simple.py` or similar test files

### Dependencies
- **Install**: `pip install -r requirements.txt` or `conda env create -f environment.yml`
- **Environment**: Create `.env` file with required variables (see README.md)

### Deployment Options

**Render Deployment**
- Command: `uvicorn app:app --host 0.0.0.0 --port $PORT`
- Requires `tesseract-ocr` apt package for OCR functionality
- Uses persistent disk storage at `/opt/render/project/src/storage`

**Railway Deployment**
- Uses nixpacks builder with `file` and `libmagic` packages
- Health checks on `/health` endpoint every 15s
- Auto-restart on failure with max 3 retries

**Docker Deployment**
- Build: `docker build -t ddbsharepoint .`
- Run: `docker run` with environment variables mounted
- Based on Python 3.11-slim with gcc for compilation

## Architecture Overview

This is a FastAPI-based Retrieval-Augmented Generation (RAG) application that integrates with SharePoint for document management and uses LlamaIndex + OpenAI for intelligent document querying.

### Core Components

**Main Application (`app.py`)**
- FastAPI application with comprehensive SharePoint integration
- Dual authentication: Basic Auth + Microsoft OAuth2
- RAG system using LlamaIndex, OpenAI embeddings, and Cohere reranking
- Document indexing with persistence to local storage
- Background component initialization to avoid startup delays

**SharePoint Integration (`sharepoint_client.py`)**
- Microsoft Graph API client for SharePoint operations
- Site/drive/file listing and download functionality
- Token-based authentication (tokens provided externally)
- Asynchronous operations with proper error handling

**Configuration (`config.py`)**
- Pydantic-based settings management
- Environment variable configuration
- SharePoint and storage settings validation
- Local file storage configuration (no cloud storage)

### Key Features

**Authentication System**
- Basic authentication for admin users
- Microsoft OAuth2 integration for SharePoint access
- Admin role checking via email whitelist (`ADMIN_EMAILS`)
- Session management with secure cookies

**Document Processing**
- Multi-format support: PDF, DOCX, TXT, images (with OCR), Excel files
- Automatic SharePoint document import and indexing
- Duplicate detection via SharePoint IDs
- Background processing with progress tracking

**RAG Query Engine**
- LlamaIndex vector store with OpenAI embeddings
- Cohere reranking for improved relevance
- Persistent storage with automatic index loading
- Enhanced query processing with follow-up question generation

**SharePoint Features**
- Browse SharePoint sites, drives, and folders
- Manual document import (admin-only)
- Automatic sync with SharePoint changes
- Special handling for specific organizational folders

### Data Flow

1. **Startup**: Background initialization of LLM, embeddings, and index loading
2. **Authentication**: User authenticates via Basic Auth or Microsoft OAuth
3. **Document Import**: Admin users can import from SharePoint, files are processed and indexed
4. **Querying**: Users submit queries, system retrieves relevant documents and generates responses
5. **Persistence**: Index changes are automatically persisted to local storage

### Environment Configuration

Required environment variables:
- `OPENAI_API_KEY`: OpenAI API access
- `COHERE_API_KEY`: Cohere reranking service
- `MS_CLIENT_ID`, `MS_CLIENT_SECRET`, `MS_TENANT_ID`: Microsoft Graph API access
- `USERNAME`, `PASSWORD`: Basic auth credentials
- `ADMIN_EMAILS`: Comma-separated list of admin email addresses
- `SESSION_SECRET_KEY`: Session encryption key

Optional configuration:
- `OPENAI_MODEL`: gpt-4o-mini, gpt-3.5-turbo (with automatic fallback)
- `CHUNK_SIZE`: Document chunking size (default: 512)
- `CHUNK_OVERLAP`: Overlap between chunks (default: 50)
- `SIMILARITY_TOP_K`: Number of similar documents to retrieve (default: 20)
- `FILE_SIZE_LIMIT`: Maximum file upload size (default: 50MB)
- `CORS_ORIGINS`: Allowed origins for cross-origin requests

### File Organization

- `storage/`: Vector index and document storage
- `templates/`: Jinja2 HTML templates for web interface
- `static/`: Static assets (logos, icons)
- `temp/`: Temporary file processing during imports
- Test files: Various `test_*.py` files for authentication testing

### Development Notes

- The application uses async/await patterns extensively
- SharePoint operations include timeout handling and retry logic
- Admin operations are protected by role-based access control
- All document processing includes metadata preservation
- The system handles both individual files and bulk folder imports
- MSAL token caching is implemented for efficient SharePoint access

### Frontend Features

**Responsive Design**
- Desktop: Sidebar + chat interface
- Mobile: Collapsible sidebar with burger menu
- Dark mode toggle with localStorage persistence

**Real-time Features**
- Health checks every 60 seconds with auto-reconnect
- Document list refresh every 30 seconds
- Connection status monitoring

### Common Issues and Debugging

**Authentication Problems**
- Check Microsoft Graph API permissions and tenant configuration
- Verify `ADMIN_EMAILS` contains correct email addresses
- Clear browser cookies if login loops occur

**Model Initialization Issues**
- App includes automatic fallback from gpt-4o-mini to gpt-3.5-turbo
- Check OpenAI API key validity and quota
- Monitor startup logs for LLM initialization errors

**SharePoint Integration Issues**
- Verify token persistence in `storage/data/token_caches/`
- Check drive permissions and site accessibility
- Monitor Graph API rate limits