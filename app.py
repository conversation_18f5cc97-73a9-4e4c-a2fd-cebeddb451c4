import os, sys, json, logging, uuid, time, socket
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional, List
from urllib.parse import unquote, quote

from fastapi import (
    FastAPI,
    Request,
    Depends,
    HTTPException,
    Query,
    BackgroundTasks,
    UploadFile,
    File,
    Form,
    status,
    Header,
    Response,
)
from fastapi.responses import (
    JSONResponse,
    RedirectResponse,
    PlainTextResponse,
    HTMLResponse,
    StreamingResponse,
    FileResponse,
)
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.security import HTTPBasic, HTTPBasicCredentials
from starlette.middleware.sessions import SessionMiddleware
from starlette.routing import NoMatchFound
import mimetypes
import tempfile
import traceback
import asyncio
from concurrent.futures import ThreadPoolExecutor
from dotenv import load_dotenv
from sharepoint_client import SharePointClient
from config import settings
import msal

# Optional msal-extensions dependency: guard import
try:
    from msal_extensions import FilePersistence, PersistedTokenCache

    MSAL_EXTENSIONS_AVAILABLE = True
except ImportError:
    MSAL_EXTENSIONS_AVAILABLE = False
    FilePersistence = None  # Define dummy class/None if needed later
    PersistedTokenCache = None  # Define dummy class/None if needed later
    logging.warning("msal-extensions not available; persistent token cache disabled.")
# --- End guard ---
import aiohttp
from PIL import Image

# Optional OCR dependency: guard import
try:
    import pytesseract

    PYTESSERACT_AVAILABLE = True
except ImportError:
    PYTESSERACT_AVAILABLE = False
    logging.warning("pytesseract not available, image OCR will be disabled.")
import io
import shutil
import openai

# Try to import APScheduler, but don't fail if it's not installed
try:
    from apscheduler.schedulers.asyncio import AsyncIOScheduler

    APSCHEDULER_AVAILABLE = True
except ImportError:
    APSCHEDULER_AVAILABLE = False
    logging.warning("APScheduler not available; webhook renewal scheduling disabled.")

# Try to import PyPDF2, but don't fail if it's not available
try:
    import PyPDF2

    PYPDF2_AVAILABLE = True
except ImportError:
    PYPDF2_AVAILABLE = False
    logging.warning(
        "PyPDF2 not available. PDF processing will use fallback methods only."
    )

from llama_index.core import (
    VectorStoreIndex,
    SimpleDirectoryReader,
    StorageContext,
    load_index_from_storage,
    Settings as LlamaSettings,
    get_response_synthesizer,
)
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.llms.openai import OpenAI as LlamaOpenAI
from llama_index.core.retrievers import VectorIndexRetriever
from llama_index.core.query_engine import RetrieverQueryEngine
from llama_index.core.schema import MetadataMode
from llama_index.core.schema import TextNode

# Optional Cohere reranker: guard import
try:
    from llama_index.postprocessor.cohere_rerank import CohereRerank

    COHERE_RERANK_AVAILABLE = True
except ImportError:
    COHERE_RERANK_AVAILABLE = False
    logging.warning("CohereRerank not available; reranking disabled.")

# Load environment variables first
load_dotenv()

# ── basic logging ─────────────────────────────────────────────────────────────
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s  %(levelname)-8s  %(name)s │ %(message)s",
    handlers=[logging.StreamHandler()],
)
logger = logging.getLogger("ddb‑rag")


# ── helpers to figure out *one* canonical base URL ────────────────────────────
def _detect_base_url() -> str:
    """
    Guarantee that every redirect and every piece of client‑side JS
    talks to *exactly* the same origin, so the browser sends the session
    cookie back on XHR requests (localhost ≠ 127.0.0.1 otherwise).
    """
    # Render / Railway / Fly.io etc.
    if ext := os.getenv("RENDER_EXTERNAL_URL"):
        return ext.rstrip("/")

    # Determine port
    port_env = int(os.getenv("PORT", 8082))
    # In development, always use localhost
    if os.getenv("ENV", "development").lower() == "development":
        return f"http://localhost:{port_env}"

    # For other environments, derive from HOST env var
    host_env = os.getenv("HOST") or "127.0.0.1"
    # If HOST is 0.0.0.0 or 127.0.0.1, default to localhost
    if host_env in ("0.0.0.0", "127.0.0.1"):
        host_env = "localhost"
    else:
        # Validate IP format; fallback to localhost on error
        try:
            socket.inet_aton(host_env)
        except OSError:
            host_env = "localhost"

    return f"http://{host_env}:{port_env}"


BASE_URL = _detect_base_url()  # ← http://127.0.0.1:8082
REDIRECT_PATH = "/auth/callback"
EXPECTED_REDIRECT = f"{BASE_URL}{REDIRECT_PATH}"
logger.info(f"OAuth redirect URI →  {EXPECTED_REDIRECT}")

# Application host and port for direct execution
APP_HOST = os.getenv("HOST", "127.0.0.1")
APP_PORT = int(os.getenv("PORT", 8082))


# Helper function to format responses before sending JSON
def format_response(text: str) -> str:
    return text


# ── MSAL configuration ────────────────────────────────────────────────────────
MSAL_SCOPES = [
    "User.Read",
    "Sites.Read.All",
    "Files.Read.All",
    "Sites.ReadWrite.All",
]
MSAL_APP_SCOPE = ["https://graph.microsoft.com/.default"]

# Build the MSAL app instance used for user authentication
msal_app = None
if settings.MS_CLIENT_ID and settings.MS_CLIENT_SECRET and settings.MS_TENANT_ID:
    # Use msal-extensions for file-persistent token cache with automatic locking
    token_cache = None  # Default to in-memory cache
    if MSAL_EXTENSIONS_AVAILABLE:
        try:
            cache_path = settings.DATA_DIR / "token_caches" / "msal_cache.bin"
            cache_path.parent.mkdir(parents=True, exist_ok=True)
            persistence = FilePersistence(str(cache_path))
            token_cache = PersistedTokenCache(persistence)
            logger.info("Using file-persistent MSAL token cache.")
        except Exception as e:
            logger.error(
                f"Failed to initialize file-persistent token cache: {e}. Falling back to in-memory cache."
            )
            token_cache = None  # Ensure fallback on error
    else:
        logger.warning("msal-extensions not found. Using in-memory MSAL token cache.")

    msal_app = msal.ConfidentialClientApplication(
        settings.MS_CLIENT_ID,
        authority=f"https://login.microsoftonline.com/{settings.MS_TENANT_ID}",
        client_credential=settings.MS_CLIENT_SECRET.get_secret_value(),
        token_cache=token_cache,  # Pass the persistent cache or None
    )
else:
    logger.warning(
        "Microsoft credentials (MS_CLIENT_ID, MS_CLIENT_SECRET, MS_TENANT_ID) not configured. SharePoint delegated auth will not work."
    )

# ── FastAPI app and middle‑wares ──────────────────────────────────────────────
app = FastAPI()

# single session cookie, always bound to the host part of BASE_URL
_cookie_host = BASE_URL.split("://", 1)[1].split(":")[
    0
]  # e.g., 'localhost' or real domain
# Only set domain for real hosts; omit for localhost to create host-only cookie
cookie_domain = None if _cookie_host in ("localhost", "127.0.0.1") else _cookie_host
app.add_middleware(
    SessionMiddleware,
    secret_key=settings.SESSION_SECRET_KEY.get_secret_value(),
    session_cookie="rag_session",
    domain=cookie_domain,
    same_site="lax",
    max_age=3600 * 24 * 7,
    https_only=BASE_URL.startswith("https"),
)

# Initialize global variables
llm = None
embed_model = None
index = None  # Will be initialized in startup

# Model configuration is now handled through settings.OPENAI_MODEL

# Configure CORS with settings
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origin_list,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["Content-Type", "text/event-stream"],
)

# Mount static files with defensive check and templates
static_dir = Path("static")
if static_dir.exists():
    app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")
    logger.info("Static files mounted from 'static' directory")
else:
    logger.warning("'static' folder not found – skipping mount")
templates = Jinja2Templates(directory="templates")


@app.get("/config.js")
async def config_js():
    # the browser will evaluate this script before running its own JS
    return PlainTextResponse(
        f"window.__RAG_BASE = '{BASE_URL}';",
        media_type="application/javascript",
    )


# --- Auth Helpers ---
def _extract_user(session):
    """Return a user-dict if either auth method is present."""
    if "basic_user" in session and session["basic_user"].get("authenticated"):
        return session["basic_user"]
    if "ms_user" in session:
        return session["ms_user"]
    return None


async def get_current_user(request: Request):
    """Get current user from session using either authentication method."""
    session = request.session
    # <<< Log session content whenever user is checked >>>
    logger.debug(f"[Auth Check] Path: {request.url.path}, Session: {dict(session)}")
    # <<< End log >>>
    user = _extract_user(session)
    if user:
        return user
    raise HTTPException(
        status_code=401,
        detail="Not authenticated",
    )


# Create unified security object and dependency
security = HTTPBasic()
CurrentUser = Depends(get_current_user)


# --- Function to get Application Token ---
async def get_application_token() -> Optional[str]:
    """Acquire an access token for the application itself using client credentials."""
    if not msal_app:
        logger.error("MSAL application not initialized. Cannot get application token.")
        return None

    try:
        # Attempt to get token from cache first (MSAL handles caching)
        result = await asyncio.wait_for(
            asyncio.to_thread(msal_app.acquire_token_silent, MSAL_APP_SCOPE, None),
            timeout=10,
        )

        if not result:
            logger.info("No suitable app token in cache, acquiring new one...")
            result = await asyncio.wait_for(
                asyncio.to_thread(msal_app.acquire_token_for_client, MSAL_APP_SCOPE),
                timeout=10,
            )

        if "access_token" in result:
            logger.info("Successfully acquired application access token.")
            return result["access_token"]
        else:
            logger.error(
                f"Failed to acquire application token: {result.get('error_description', 'No error description')}"
            )
            return None
    except Exception as e:
        logger.error(f"Exception acquiring application token: {e}", exc_info=True)
        return None


@app.on_event("startup")
async def startup_event():
    """Initialize application state and create required directories and schedule heavy tasks."""
    logger.info("Starting application with document persistence...")
    logger.info(f"Python version: {sys.version}")
    logger.info(f"Current working directory: {os.getcwd()}")
    logger.info(f"Environment: {os.getenv('ENV', 'development')}")

    # <<< Add route logging >>>
    logger.info("Registered routes:")
    for r in app.routes:
        if hasattr(r, "methods"):  # Check if it's a route with methods
            try:
                logger.info(
                    f"  - Route: Path={r.path}, Name={r.name}, Methods={r.methods}"
                )
            except AttributeError:  # Handle routes without a name
                logger.info(f"  - Route: Path={r.path}, Methods={r.methods}")
        elif hasattr(r, "path"):  # Handle Mounts (like static files)
            logger.info(f"  - Mount: Path={r.path}")
        else:
            logger.info(f"  - Other: {r}")  # Log anything else unexpected
    # <<< End route logging >>>

    # Create required directories
    try:
        settings.STORAGE_DIR.mkdir(exist_ok=True)
        settings.DATA_DIR.mkdir(exist_ok=True)
        settings.UPLOAD_DIR.mkdir(exist_ok=True)
        settings.TEMP_DIR.mkdir(exist_ok=True)
        logger.info("Storage directory ready")
    except Exception as e:
        logger.error(f"Error creating directories: {e}")

    # Schedule heavy initialization in background
    loop = asyncio.get_running_loop()
    loop.create_task(initialise_heavy_components())
    logger.info("Scheduled background initialization of heavy components")


# Background initialization moved out of startup_event
async def initialise_heavy_components():
    """Runs expensive initialization after the app has started."""
    try:
        # Initialize OpenAI components
        logger.info("Initializing OpenAI model...")
        # Validate model name before initialization
        validate_model_name(settings.OPENAI_MODEL)
        logger.info(f"Using validated OpenAI model: {settings.OPENAI_MODEL}")
        
        openai.api_key = settings.OPENAI_API_KEY.get_secret_value()
        app.state.llm = await asyncio.to_thread(
            LlamaOpenAI, model=settings.OPENAI_MODEL, temperature=0.7, max_tokens=2000
        )
        logger.info("OpenAI LLM initialized successfully")

        # Initialize embedding model
        logger.info("Initializing embedding model...")
        app.state.embed_model = await asyncio.to_thread(OpenAIEmbedding, model="text-embedding-3-small")
        logger.info("Embedding model initialized successfully")
        
        # Set global LlamaSettings with the initialized models for consistent behavior
        # This ensures all LlamaIndex components use the same model instances
        logger.info("Setting global LlamaSettings with initialized models...")
        LlamaSettings.llm = app.state.llm
        LlamaSettings.embed_model = app.state.embed_model
        logger.info("Global LlamaSettings configured successfully")

        # Initialize Cohere reranker if the module is available
        if COHERE_RERANK_AVAILABLE:
            try:
                logger.info("Initializing Cohere reranker...")
                app.state.reranker = await asyncio.to_thread(
                    CohereRerank, 
                    api_key=settings.COHERE_API_KEY.get_secret_value(),
                    model="rerank-english-v3.0"
                )
                logger.info("Cohere reranker initialized successfully")
            except Exception as e:
                logger.error(f"Error initializing Cohere reranker: {e}")
                app.state.reranker = None
        else:
            app.state.reranker = None
            logger.info("CohereRerank not available; skipping initialization")

        # Initialize SharePoint client if enabled
        if settings.USE_SHAREPOINT:
            try:
                logger.info("Initializing SharePoint client...")
                app.state.sharepoint_client = await asyncio.to_thread(SharePointClient)
                logger.info("SharePoint client initialized successfully")
            except Exception as e:
                logger.error(f"Error initializing SharePoint client: {e}")

        # Load vector index
        try:
            logger.info("Loading vector index...")
            app.state.index = await load_index()
            logger.info("Index loaded successfully")
        except Exception as e:
            logger.error(f"Error loading index: {e}")
            logger.warning("Creating empty index as fallback...")
            app.state.index = await create_empty_index()

        # Create query engine
        try:
            app.state.query_engine = get_query_engine(
                app.state.index, app.state.reranker
            )
            logger.info("Query engine created successfully")
        except Exception as e:
            logger.error(f"Error creating query engine: {e}")

    except Exception as e:
        logger.error(f"Error in background initialization: {e}", exc_info=True)


def validate_model_name(model_name):
    """Validate that the model name is safe and doesn't contain URL parameters or invalid characters."""
    if not model_name or not isinstance(model_name, str):
        raise ValueError(f"Invalid model name: {model_name}")
    
    # Check for URL parameter contamination
    invalid_patterns = ['=', '&', '?', ':', 'login_success', 'http', 'https']
    for pattern in invalid_patterns:
        if pattern in model_name:
            raise ValueError(f"Model name contains invalid pattern '{pattern}': {model_name}")
    
    # Check that it's a reasonable model name
    if len(model_name) < 3 or len(model_name) > 50:
        raise ValueError(f"Model name has invalid length: {model_name}")
    
    return True


def create_model_instances():
    """Create isolated LLM and embedding model instances to avoid global state corruption."""
    try:
        # Validate model name before creating instances
        validate_model_name(settings.OPENAI_MODEL)
        logger.info(f"Creating model instances with validated model: {settings.OPENAI_MODEL}")
        
        llm = LlamaOpenAI(
            model=settings.OPENAI_MODEL,
            temperature=0.7,
            max_tokens=2000,
        )
        embed_model = OpenAIEmbedding(model="text-embedding-3-small")
        logger.info("Created isolated model instances successfully")
        return llm, embed_model
    except Exception as e:
        logger.error(f"Error creating model instances: {str(e)}")
        raise


async def create_empty_index():
    """Create an empty index without documents using isolated model instances."""
    try:
        logger.info("Creating new empty index...")
        # Create isolated model instances to avoid global state corruption
        llm, embed_model = create_model_instances()
        
        # Temporarily set global settings for index creation, then restore
        original_llm = getattr(LlamaSettings, 'llm', None)
        original_embed_model = getattr(LlamaSettings, 'embed_model', None)
        
        try:
            LlamaSettings.llm = llm
            LlamaSettings.embed_model = embed_model
            
            # Create an empty vector store index
            index = VectorStoreIndex([])
            
            # Persist the empty index to storage
            storage_context = index.storage_context
            storage_context.persist(persist_dir=str(settings.STORAGE_DIR))
            logger.info("Successfully created and persisted empty index")
            
            return index
        finally:
            # Restore original global settings to prevent contamination
            if original_llm is not None:
                LlamaSettings.llm = original_llm
            elif hasattr(LlamaSettings, 'llm'):
                delattr(LlamaSettings, 'llm')
            
            if original_embed_model is not None:
                LlamaSettings.embed_model = original_embed_model
            elif hasattr(LlamaSettings, 'embed_model'):
                delattr(LlamaSettings, 'embed_model')
                
    except Exception as e:
        logger.error(f"Error creating empty index: {str(e)}")
        # Return a non-persisted index as fallback
        logger.warning("Creating non-persisted fallback index")
        return VectorStoreIndex([])


def get_query_engine(index, reranker=None):
    """Create a query engine from the index and reranker."""
    try:
        logger.info("Setting up query engine...")
        # Create retriever with increased similarity_top_k
        retriever = VectorIndexRetriever(
            index=index,
            similarity_top_k=10,  # Increased from default
        )
        logger.info("Created vector index retriever")

        # Create response synthesizer with more detailed configuration
        response_synthesizer = get_response_synthesizer(
            response_mode="compact",
            verbose=True,
            streaming=True,
        )
        logger.info("Created response synthesizer")

        # Create query engine
        query_engine = RetrieverQueryEngine(
            retriever=retriever,
            response_synthesizer=response_synthesizer,
            node_postprocessors=[reranker] if reranker else [],
        )
        logger.info("Query engine created successfully")
        return query_engine
    except Exception as e:
        logger.error(f"Error creating query engine: {str(e)}")
        return None


# Function to load or create the vector index
async def load_index():
    """Load the vector index from storage or create a new one if it doesn't exist."""
    logger.info("Attempting to load index from local storage...")
    storage_dir = settings.STORAGE_DIR
    try:
        if storage_dir.exists() and any(storage_dir.iterdir()):
            storage_context = StorageContext.from_defaults(persist_dir=str(storage_dir))
            index = load_index_from_storage(storage_context)
            logger.info("Index loaded successfully from storage")
            return index
        else:
            logger.warning("No existing index found; creating empty index")
            return await create_empty_index()
    except Exception as e:
        logger.error(f"Error loading index: {e}", exc_info=True)
        logger.warning("Falling back to creating empty index")
        return await create_empty_index()


async def persist_index():
    """Persists the current index state to disk."""
    if hasattr(app.state, "index") and hasattr(app.state.index, "storage_context"):
        try:
            logger.info("Persisting index changes to storage...")
            # Run the potentially blocking I/O in a separate thread
            await asyncio.to_thread(
                app.state.index.storage_context.persist,
                persist_dir=str(settings.STORAGE_DIR),
            )
            logger.info("Index changes persisted successfully.")
        except Exception as e:
            logger.error(f"Error persisting index changes: {e}", exc_info=True)
    else:
        logger.warning("Index or storage context not available, cannot persist.")


@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup resources on application shutdown."""
    if hasattr(app.state, "index"):
        try:
            logger.info("Persisting index to storage...")
            await persist_index()
            logger.info("Index persisted successfully")
        except Exception as e:
            logger.error(f"Error persisting index: {e}")

    # --- Optional: Shutdown scheduler and delete subscription ---
    if hasattr(app.state, "scheduler") and app.state.scheduler.running:
        logger.info("Shutting down webhook renewal scheduler...")
        app.state.scheduler.shutdown()

    if (
        hasattr(app.state, "sharepoint_subscription_id")
        and app.state.sharepoint_subscription_id
        and hasattr(app.state, "sharepoint_client")
    ):
        logger.info(
            f"Attempting to delete webhook subscription: {app.state.sharepoint_subscription_id}"
        )
        app_token = await get_application_token()
        if app_token:
            try:
                await app.state.sharepoint_client.delete_webhook_subscription(
                    app.state.sharepoint_subscription_id, app_token
                )
                logger.info("Successfully deleted webhook subscription.")
            except Exception as e:
                logger.error(f"Failed to delete webhook subscription on shutdown: {e}")
        else:
            logger.warning(
                "Could not get application token to delete webhook subscription on shutdown."
            )


# --- Basic Auth Endpoints and Helpers ---
async def get_microsoft_access_token(request: Request) -> str:
    """Get Microsoft Graph API access token from session using server-side cache."""
    session = request.session
    ms_user = session.get("ms_user")
    if not ms_user:
        # Not logged in: API gets 401, browser redirects
        if request.url.path.startswith("/api/"):
            raise HTTPException(
                status_code=401, detail="Microsoft authentication required"
            )
        return RedirectResponse(url=request.url_for("login_microsoft"), status_code=302)

    # Acquire token silently
    try:
        # Pick the first account in the cache to use for silent token acquisition
        accounts = msal_app.get_accounts()
        account = accounts[0] if accounts else None
        result = await asyncio.wait_for(
            asyncio.to_thread(msal_app.acquire_token_silent, MSAL_SCOPES, account),
            timeout=10,
        )
    except Exception as e:
        logger.error(f"Error acquiring token silently: {e}")
        if request.url.path.startswith("/api/"):
            raise HTTPException(
                status_code=502, detail="Could not reach Azure AD token endpoint"
            )
        # <<< Store target URL before redirecting >>>
        session["post_auth_redirect_url"] = str(request.url)
        logger.info(
            f"Storing post-auth redirect URL: {session['post_auth_redirect_url']}"
        )
        # <<< End store >>>
        return RedirectResponse(url=request.url_for("login_microsoft"), status_code=302)

    access_token = result.get("access_token") if isinstance(result, dict) else None
    if not access_token:
        # Silent acquisition failed: redirect to login
        logger.warning("Silent token acquisition failed, redirecting to login")
        if request.url.path.startswith("/api/"):
            raise HTTPException(
                status_code=401, detail="Microsoft authentication required"
            )
        # <<< Store target URL before redirecting >>>
        session["post_auth_redirect_url"] = str(request.url)
        logger.info(
            f"Storing post-auth redirect URL: {session['post_auth_redirect_url']}"
        )
        # <<< End store >>>
        return RedirectResponse(url=request.url_for("login_microsoft"), status_code=302)

    return access_token


MsTokenDep = Depends(get_microsoft_access_token)


# --- Basic Auth Endpoints ---
@app.post("/login")
async def login(
    request: Request, credentials: HTTPBasicCredentials = Depends(security)
):
    # Verify credentials against settings (environment variables)
    correct_username = settings.USERNAME
    correct_password = settings.PASSWORD.get_secret_value()

    # Simple credential validation
    if not (
        credentials.username == correct_username
        and credentials.password == correct_password
    ):
        logger.warning(f"Failed login attempt for user: {credentials.username}")
        raise HTTPException(
            status_code=401,
            detail="Invalid credentials",
            headers={"WWW-Authenticate": "Basic"},
        )

    # --- Clear previous session data before setting new ---
    session = request.session
    session.clear()
    # --------------------------------------------------------

    # Set user in session (only Basic Auth info)
    session["basic_user"] = {
        "username": credentials.username,
        "authenticated": True,
    }
    # Optionally set the general 'authenticated' flag if needed by other parts
    session["authenticated"] = True
    session["auth_method"] = "basic"

    logger.info(f"User '{credentials.username}' successfully logged in via Basic Auth")

    # Return success
    return {"authenticated": True, "username": credentials.username}


# New login endpoint
@app.get("/login")
async def login_page():
    """Display login page with Microsoft login button."""
    return HTMLResponse(
        """
        <div style="font-family: Arial, sans-serif; max-width: 400px; margin: 100px auto; padding: 20px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); text-align: center;">
            <img src="static/ddb-logo-login.png" alt="DDB Logo" style="width: 120px; margin: 0 auto 1.5rem; display: block;" onerror="this.onerror=null; this.src='static/ddb-logo-80x80.png';">
            <h1 style="font-size: 1.5rem; margin-bottom: 2rem; color: #ffd100;">DDBrain</h1>
            <a href="/login/microsoft" style="display: block; background-color: #0078d4; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; font-weight: bold;">
                Sign in with Microsoft
            </a>
        </div>
        """
    )


@app.post("/logout")
async def logout(request: Request):
    # Clear the session
    session = request.session
    session.clear()
    logger.info("User logged out")
    return {"message": "Successfully logged out"}


@app.get("/user")
async def get_user_status(request: Request):
    # Get user from session or return unauthenticated
    session = request.session
    # <<< Log session content for debugging >>>
    logger.info(f"[/user] Session content: {dict(session)}")
    user = _extract_user(session)
    if user:
        logger.info(f"[/user] Found user in session: {user}")
        user["is_admin"] = user_is_admin(session)
        return user
    logger.info("[/user] No user found in session.")
    return {"authenticated": False, "is_admin": False}


@app.get("/login/microsoft", name="login_microsoft")
async def login_microsoft(request: Request, login_hint: Optional[str] = Query(None)):
    """Initiate Microsoft OAuth flow, optionally using a login hint."""
    try:
        if not msal_app:
            logger.error("Microsoft authentication is not configured")
            return templates.TemplateResponse(
                "error.html",
                {
                    "request": request,
                    "error_title": "Microsoft Authentication Not Configured",
                    "error_message": "Microsoft authentication is not properly configured.",
                    "error_details": "Please check your environment variables: MS_CLIENT_ID, MS_CLIENT_SECRET, and MS_TENANT_ID",
                },
                status_code=500,
            )

        # Generate state for CSRF protection
        session = request.session
        state = str(uuid.uuid4())
        session["ms_auth_state"] = state
        logger.info(f"Generated MS auth state: {state}")
        if login_hint:
            logger.info(f"Received login_hint: {login_hint}")
        else:
            logger.info("No login_hint received.")

        # Generate the authorization URL, passing the hint if present
        auth_uri = msal_app.get_authorization_request_url(
            MSAL_SCOPES,
            state=state,
            redirect_uri=EXPECTED_REDIRECT,  # Use the correctly constructed URI
            login_hint=login_hint,  # <<< Pass the hint here
        )

        # Log the generated URL before redirecting
        logger.info(f"Generated Microsoft auth URL for redirect: {auth_uri}")

        # Return the redirect response
        # Use 303 See Other for POST-like behavior after GET
        return RedirectResponse(auth_uri, status_code=303)

    except Exception as e:
        logger.error(f"Error during Microsoft login initiation: {e}", exc_info=True)
        return templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "error_title": "Microsoft Login Error",
                "error_message": "Failed to initiate Microsoft login.",
                "error_details": f"Error details: {str(e)}",
            },
            status_code=500,
        )


@app.get("/auth/callback")
async def auth_callback(
    request: Request,
    code: str = None,
    state: str = None,
    error: str = None,
    error_description: str = None,
):
    """Handle the Microsoft OAuth callback."""
    try:
        session = request.session
        logger.info(
            f"[/auth/callback] Starting. Session before processing: {dict(session)}"
        )

        # Handle potential errors from Microsoft
        if error:
            logger.error(f"Microsoft OAuth error: {error} - {error_description}")
            return templates.TemplateResponse(
                "error.html",
                {
                    "request": request,
                    "error_title": "Microsoft Authentication Error",
                    "error_message": f"Error: {error}",
                    "error_details": error_description or "No details provided",
                },
                status_code=400,
            )

        # Verify state parameter to prevent CSRF attacks
        if not state or session.get("ms_auth_state") != state:
            expected_state = session.get("ms_auth_state", "No state in session")
            logger.error(
                f"State mismatch. Expected: {expected_state}, Received: {state}"
            )
            return templates.TemplateResponse(
                "error.html",
                {
                    "request": request,
                    "error_title": "Authentication Error",
                    "error_message": "Invalid authentication state.",
                    "error_details": "The state parameter did not match. This could indicate a cross-site request forgery attempt.",
                },
                status_code=400,
            )

        # Clear state after verification
        session.pop("ms_auth_state", None)
        logger.info(
            f"[/auth/callback] State verified and popped. Session: {dict(session)}"
        )

        # Process the callback with authorization code
        if code:
            # Exchange code for token in a background thread with timeout
            result = await asyncio.wait_for(
                asyncio.to_thread(
                    msal_app.acquire_token_by_authorization_code,
                    code,
                    MSAL_SCOPES,
                    EXPECTED_REDIRECT,
                ),
                timeout=60,
            )

            if "error" in result:
                logger.error(
                    f"Token acquisition error: {result.get('error')} - {result.get('error_description')}"
                )
                return templates.TemplateResponse(
                    "error.html",
                    {
                        "request": request,
                        "error_title": "Token Acquisition Error",
                        "error_message": f"Error: {result.get('error')}",
                        "error_details": result.get(
                            "error_description", "No details provided"
                        ),
                    },
                    status_code=400,
                )

            # Successfully acquired token
            logger.info("Token acquired successfully")

            # Get token claims and user info
            id_token_claims = result.get("id_token_claims", {})
            username = id_token_claims.get("preferred_username", "unknown")
            email = id_token_claims.get("email", username)
            name = id_token_claims.get("name", "User")

            logger.info(f"Microsoft login successful for {email}")

            # === Store the intended redirect target BEFORE clearing ===
            redirect_target = session.get("post_auth_redirect_url")
            logger.info(f"Retrieved redirect target before clear: {redirect_target}")
            # === End store target ===

            # Clear any existing session data first (wipes redirect_target from session)
            session.clear()
            logger.info("Session cleared after successful Microsoft token acquisition.")

            # === Set session data based *only* on Microsoft login ===
            # # Remove conditional restoration based on was_basic_admin
            # if was_basic_admin:
            #     session["basic_user"] = {
            #         "username": settings.USERNAME,  # Use the actual admin username
            #         "authenticated": True,
            #     }
            #     logger.info("Restored Basic Auth admin user to session.")
            # else:
            #     # If not restoring admin, set basic_user based on MS identity
            #     # --- We don't need basic_user if logged in via MS ---
            #     # session["basic_user"] = {
            #     #     "username": email,  # Use MS email as username
            #     #     "authenticated": True,
            #     # }
            #     pass # No need to set basic_user here

            # Store Microsoft user info
            session["ms_user"] = {"email": email, "name": name}
            logger.info(
                f"Set ms_user in session: {{'email': '{email}', 'name': '{name}'}}"
            )

            # Set general flags
            session["authenticated"] = True
            session["auth_method"] = "microsoft"
            logger.info(
                "Set authenticated=True and auth_method='microsoft' in session."
            )

            # Store token cache if available
            access_token = result.get("access_token")
            expires_in = result.get("expires_in")  # Duration in seconds
            # Calculate expiry time (epoch seconds) for easier checking later
            expires_on = time.time() + expires_in if expires_in else None
            if access_token and expires_on:
                session["ms_token_cache"] = {
                    "access_token": access_token,
                    "expires_on": expires_on,
                }
            else:
                # Ensure we log if the token cache *couldn't* be set
                logger.warning(
                    "ms_token_cache was NOT set in session (missing token or expiry)."
                )
            # === End setting session data ===

            # Log session JUST BEFORE redirect to confirm state
            logger.info(
                f"[/auth/callback] Session state before creating redirect response: {dict(session)}"
            )

            # === Determine redirect URL ===
            # Use the local variable captured before session clear
            # redirect_url = session.pop("post_auth_redirect_url", None)
            if not redirect_target:
                logger.info("No redirect_target stored, defaulting to homepage.")
                redirect_url = "/?login_success=true"  # Default redirect
            else:
                logger.info(f"Using stored redirect_target: {redirect_target}")
                redirect_url = redirect_target  # Use the stored URL
                # Optionally add the login_success flag if redirecting elsewhere
                if "?" in redirect_url:
                    redirect_url += "&login_success=true"
                else:
                    redirect_url += "?login_success=true"
            # === End determine redirect URL ===

            # Create response AFTER setting session data
            response = RedirectResponse(url=redirect_url, status_code=303)

            # Return the response (session middleware will handle saving it)
            return response
        else:
            logger.error("No authorization code provided in callback")
            return templates.TemplateResponse(
                "error.html",
                {
                    "request": request,
                    "error_title": "Authentication Error",
                    "error_message": "No authorization code provided.",
                    "error_details": "The OAuth callback did not contain an authorization code.",
                },
                status_code=400,
            )

    except Exception as e:
        logger.error(f"Error during OAuth callback: {e}", exc_info=True)
        return templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "error_title": "Authentication Error",
                "error_message": "An unexpected error occurred during authentication.",
                "error_details": str(e),
            },
            status_code=500,
        )


@app.get("/sharepoint/sites", name="list_sharepoint_sites")
async def list_sharepoint_sites_view(request: Request):
    """Render the SharePoint sites view for the authenticated user."""
    # <<< Add early entry log >>>
    logger.info("Entering list_sharepoint_sites_view")
    # <<< End early entry log >>>
    try:
        # <<< Log session content on entry >>>
        logger.info(
            f"[/sharepoint/sites] Session content on entry: {dict(request.session)}"
        )
        # <<< End log >>>

        logger.info("Starting list_sharepoint_sites_view endpoint")
        logger.info(f"Session content at /sharepoint/sites: {dict(request.session)}")

        # First verify SharePoint client is initialized
        if not hasattr(app.state, "sharepoint_client"):
            logger.error("SharePoint client is not initialized.")
            return templates.TemplateResponse(
                "error.html",
                {
                    "request": request,
                    "error_title": "SharePoint Not Configured",
                    "error_message": "SharePoint integration is not configured or failed to initialize.",
                    "error_details": "Check your server configuration and environment variables.",
                },
                status_code=500,
            )

        # Check for authentication
        session = request.session
        is_authenticated = False
        if "basic_user" in session and session["basic_user"].get("authenticated"):
            is_authenticated = True
        elif "ms_user" in session:
            is_authenticated = True

        if not is_authenticated:
            logger.warning(
                f"No authentication found in session. Session keys: {list(session.keys())}"
            )
            # <<< Store target URL before redirecting >>>
            session["post_auth_redirect_url"] = str(request.url)
            logger.info(
                f"Storing post-auth redirect URL: {session['post_auth_redirect_url']}"
            )
            # <<< End store >>>
            redirect_url = request.url_for("login_microsoft")
            return RedirectResponse(url=str(redirect_url), status_code=302)

        # Get the access token
        if "ms_token_cache" not in session:
            logger.warning(
                "No Microsoft token cache found in session, redirecting to login"
            )
            # <<< Store target URL before redirecting >>>
            session["post_auth_redirect_url"] = str(request.url)
            logger.info(
                f"Storing post-auth redirect URL: {session['post_auth_redirect_url']}"
            )
            # <<< End store >>>
            redirect_url = request.url_for("login_microsoft")
            return RedirectResponse(url=str(redirect_url), status_code=302)

        cached_token = session.get("ms_token_cache", {})
        access_token = cached_token.get("access_token")

        if not access_token:
            logger.warning(
                "No valid access token found in token cache, redirecting to login"
            )
            # Clear any invalid session data
            if "ms_token_cache" in session:
                del session["ms_token_cache"]
            if "ms_user" in session:
                del session["ms_user"]
            # <<< Store target URL before redirecting >>>
            session["post_auth_redirect_url"] = str(request.url)
            logger.info(
                f"Storing post-auth redirect URL: {session['post_auth_redirect_url']}"
            )
            # <<< End store >>>
            redirect_url = request.url_for("login_microsoft")
            return RedirectResponse(url=str(redirect_url), status_code=302)

        try:
            # List SharePoint sites
            logger.info("Calling SharePoint client to list sites")
            try:
                sites = await asyncio.wait_for(
                    app.state.sharepoint_client.list_sites(access_token), timeout=10
                )
            except asyncio.TimeoutError:
                logger.error("Timeout listing SharePoint sites")
                raise HTTPException(status_code=504, detail="Timeout listing sites")

            # Log the results for debugging
            site_count = len(sites) if sites else 0
            logger.info(f"Found {site_count} SharePoint sites")

            # Store the first site ID in the session (assuming it's the one we want)
            if sites and len(sites) > 0:
                session["current_site_id"] = sites[0].get("id", "")
                logger.info(f"Stored site ID in session: {session['current_site_id']}")

            return templates.TemplateResponse(
                "sharepoint_sites.html", {"request": request, "sites": sites}
            )
        except Exception as e:
            error_message = str(e).lower()
            # Check for any token-related errors
            if any(
                keyword in error_message
                for keyword in ["token", "401", "unauthorized", "expired"]
            ):
                logger.info("Token expired or invalid, redirecting to Microsoft login")
                # Clear the expired token
                if "ms_token_cache" in session:
                    del session["ms_token_cache"]
                if "ms_user" in session:
                    del session["ms_user"]
                redirect_url = request.url_for("login_microsoft")
                return RedirectResponse(url=str(redirect_url), status_code=302)
            raise  # Re-raise other exceptions

    except Exception as e:
        logger.error(f"Error in list_sharepoint_sites_view: {e}", exc_info=True)
        return templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "error_title": "SharePoint Error",
                "error_message": str(e),
                "error_details": f"Exception type: {type(e).__name__}",
            },
            status_code=500,
        )


@app.get("/sharepoint/drives/{site_id}", name="list_sharepoint_drives")
async def list_sharepoint_drives(
    request: Request, site_id: str, user=CurrentUser, ms_token: str = MsTokenDep
):
    """List document libraries (drives) within a SharePoint site."""
    if not hasattr(app.state, "sharepoint_client"):
        logger.error("SharePoint client is not initialized.")
        raise HTTPException(
            status_code=500,
            detail="SharePoint integration is not configured or failed to initialize.",
        )

    try:
        logger.info(f"Listing drives for site ID: {site_id}")
        logger.info(f"Token length: {len(ms_token) if ms_token else 0} characters")

        # --- MODIFICATION START ---
        # Define the target site ID components and repository path
        # Site ID format: {hostname},{siteCollectionId},{siteId}
        target_hostname = "ddbgroupcomph-my.sharepoint.com"
        target_site_collection_id = "c05ba4f2-c263-4c53-a6be-e3e094c754d4"
        target_site_id_component = "40d66325-d5a1-4aa6-b0f4-0caa6705ed2d"
        target_full_site_id = (
            f"{target_hostname},{target_site_collection_id},{target_site_id_component}"
        )
        # --- Use drive-relative path (try root level) ---
        target_repo_path = "DDB Group Repository"
        # --- End path change ---

        # --- URL-decode the incoming site_id from the path ---
        site_id_decoded = unquote(site_id)
        logger.info(f"Decoded site_id for comparison: {site_id_decoded}")
        # --- End decode ---

        # Check if the requested site_id matches the target IT Storage site ID
        if site_id_decoded == target_full_site_id:
            logger.info(
                f"Detected target IT Storage site ID: {site_id_decoded}. Attempting to get its primary drive and redirect."
            )
            try:
                # Get drives specifically for the target site
                # Use the original (potentially encoded) site_id for the API call
                drives = await asyncio.wait_for(
                    app.state.sharepoint_client.list_drives(
                        site_id=site_id, token=ms_token
                    ),
                    timeout=10,
                )

                if drives:
                    # Assume the first drive is the primary one for a personal site
                    target_drive = drives[0]
                    target_drive_id = target_drive.get("id")
                    logger.info(
                        f"Found primary drive ID for IT Storage site: {target_drive_id}"
                    )

                    if target_drive_id:
                        try:
                            # Construct URL manually to avoid issues with url_for and query params
                            base_url_path = request.url_for(
                                "list_sharepoint_files", drive_id=target_drive_id
                            )
                            # URL-encode the folder path for the query string
                            encoded_folder_path = quote(target_repo_path, safe="")
                            redirect_url = (
                                f"{base_url_path}?folder_path={encoded_folder_path}"
                            )

                            logger.info(
                                f"Redirecting to IT Storage DDB Group Repository (manual URL): {redirect_url}"
                            )
                            return RedirectResponse(
                                url=str(redirect_url), status_code=302
                            )
                        except NoMatchFound:  # This except block should now work
                            logger.error(
                                f"Could not generate BASE URL for list_sharepoint_files (drive_id: {target_drive_id}). Route misconfigured?",
                                exc_info=True,
                            )
                            # Fall through to standard behavior if base URL generation fails
                        except Exception as url_err:  # Catch other potential errors during manual URL construction
                            logger.error(
                                f"Error constructing redirect URL: {url_err}",
                                exc_info=True,
                            )
                            # Fall through
                    else:
                        logger.warning(
                            "Could not extract drive ID from the drives list for IT Storage site."
                        )
                else:
                    logger.warning(
                        f"No drives found for the target IT Storage site ID: {site_id}"
                    )

            except asyncio.TimeoutError:
                logger.error(
                    f"Timeout listing drives for target IT Storage site {site_id}"
                )
                # Fall through to standard error handling
            except Exception as e:
                logger.error(
                    f"Error processing target IT Storage site {site_id}: {e}",
                    exc_info=True,
                )
                # Fall through to standard error handling

            # If redirect failed for any reason, fall through to rendering the drive list page for IT Storage
            logger.warning(
                f"Redirect failed for IT Storage site {site_id}. Falling back to rendering drive list."
            )
            # Note: 'drives' might be populated here from the attempt above
            if (
                "drives" not in locals()
            ):  # Ensure drives is defined if list_drives failed above
                drives = []
            return templates.TemplateResponse(
                "sharepoint_drives.html",
                {"request": request, "drives": drives, "site_id": site_id},
            )
        else:
            logger.info(
                f"Requested site_id {site_id} does not match target IT Storage site. Proceeding with standard drive listing."
            )
        # --- MODIFICATION END ---

        # Standard approach: List drives for non-target sites
        logger.info(f"Proceeding with standard drive listing for site_id: {site_id}")
        try:
            drives = await asyncio.wait_for(
                app.state.sharepoint_client.list_drives(
                    site_id=site_id, token=ms_token
                ),
                timeout=10,
            )
        except asyncio.TimeoutError:
            logger.error(f"Timeout listing drives for site {site_id}")
            raise HTTPException(status_code=504, detail="Timeout listing drives")
        logger.info(f"Found {len(drives)} drives for site {site_id}")

        return templates.TemplateResponse(
            "sharepoint_drives.html",
            {"request": request, "drives": drives, "site_id": site_id},
        )
    except Exception as e:
        logger.error(f"Error listing drives for site {site_id}: {e}", exc_info=True)

        # Check if the error is likely due to token issues (e.g., Graph API errors)
        error_detail = f"Failed to list document libraries: {e}"
        status_code = 500

        if "401" in str(e) or "Unauthorized" in str(e):
            status_code = 401
            error_detail = "Failed to list document libraries: Authentication failed or token expired. Please try logging out and back in via Microsoft."

        return templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "error_title": "SharePoint Error",
                "error_message": error_detail,
                "error_details": f"Exception type: {type(e).__name__}, Message: {str(e)}",
            },
            status_code=status_code,
        )


@app.get("/sharepoint/files/{drive_id}", name="list_sharepoint_files")
async def list_sharepoint_files(
    request: Request,
    drive_id: str,
    folder_path: str = "",
    user=CurrentUser,
    ms_token: str = MsTokenDep,
):
    """List files and folders in a SharePoint drive or folder."""
    if not hasattr(app.state, "sharepoint_client"):
        logger.error("SharePoint client is not initialized.")
        raise HTTPException(
            status_code=500,
            detail="SharePoint integration is not configured or failed to initialize.",
        )

    try:
        logger.info(
            f"Listing files for drive ID: {drive_id}, folder path: {folder_path}"
        )
        logger.info(f"Token length: {len(ms_token) if ms_token else 0} characters")

        # Get the files from SharePoint
        try:
            files = await asyncio.wait_for(
                app.state.sharepoint_client.list_files(
                    drive_id=drive_id, token=ms_token, folder_path=folder_path
                ),
                timeout=10,
            )
        except asyncio.TimeoutError:
            logger.error(f"Timeout listing files for drive {drive_id}")
            raise HTTPException(status_code=504, detail="Timeout listing files")
        logger.info(f"Found {len(files)} files/folders")

        # Get the site ID from the session or a default value
        site_id = request.session.get("current_site_id", "")

        return templates.TemplateResponse(
            "sharepoint_files.html",
            {
                "request": request,
                "files": files,
                "drive_id": drive_id,
                "current_path": folder_path,
                "site_id": site_id,
            },
        )
    except Exception as e:
        logger.error(f"Error listing files for drive {drive_id}: {e}", exc_info=True)

        # Check if the error is likely due to token issues
        error_detail = f"Failed to list files: {e}"
        status_code = 500

        if "401" in str(e) or "Unauthorized" in str(e):
            status_code = 401
            error_detail = "Failed to list files: Authentication failed or token expired. Please try logging out and back in via Microsoft."

        return templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "error_title": "SharePoint Error",
                "error_message": error_detail,
                "error_details": f"Exception type: {type(e).__name__}, Message: {str(e)}",
            },
            status_code=status_code,
        )


async def add_document_to_index(file_path: str, metadata: dict):
    """Loads a document from file_path, adds metadata, and inserts into the index."""
    try:
        file_name_for_log = metadata.get("file_name", Path(file_path).name)
        logger.info(
            f"Attempting to index document: {file_path} (File: {file_name_for_log})"
        )

        if not hasattr(app.state, "index") or not app.state.index:
            logger.error(f"Index not available, cannot add document: {file_path}")
            return

        # Check for existing document with same SharePoint ID
        sharepoint_id = metadata.get("sharepoint_id")
        if sharepoint_id and hasattr(app.state.index, "docstore"):
            # Find and remove any existing documents with the same SharePoint ID
            docs_to_delete = []
            for doc_id, doc_info in app.state.index.docstore.docs.items():
                if (
                    doc_info.metadata
                    and doc_info.metadata.get("sharepoint_id") == sharepoint_id
                ):
                    docs_to_delete.append(doc_id)

            # Delete any found duplicates
            for doc_id in docs_to_delete:
                await asyncio.to_thread(
                    app.state.index.delete_ref_doc, doc_id, delete_from_docstore=True
                )
                logger.info(
                    f"Removed existing document with SharePoint ID {sharepoint_id}"
                )

        # Use SimpleDirectoryReader to load the document
        reader = SimpleDirectoryReader(input_files=[file_path])
        documents = await asyncio.to_thread(reader.load_data)

        if not documents:
            logger.warning(f"No content extracted from document: {file_path}")
            return

        nodes_to_insert = []
        for doc in documents:
            # Combine existing doc metadata with SharePoint metadata
            combined_metadata = {**doc.metadata, **metadata}

            # Ensure required metadata fields
            combined_metadata.setdefault(
                "file_name", metadata.get("file_name", Path(file_path).name)
            )
            combined_metadata.setdefault("web_url", metadata.get("web_url", ""))
            doc.metadata = combined_metadata

            # Create TextNode for insertion
            node = TextNode(
                text=doc.get_content(metadata_mode=MetadataMode.NONE),
                metadata=doc.metadata,
            )
            nodes_to_insert.append(node)

        if nodes_to_insert:
            # Insert nodes into the index
            await asyncio.to_thread(app.state.index.insert_nodes, nodes_to_insert)
            # Persist changes
            await persist_index()
            logger.info(
                f"Successfully added/updated {len(nodes_to_insert)} nodes in index from: {file_name_for_log}"
            )
        else:
            logger.info(f"No nodes generated for indexing from: {file_name_for_log}")

    except Exception as e:
        logger.error(
            f"Failed to index document {metadata.get('file_name', file_path)}: {e}",
            exc_info=True,
        )


async def import_sharepoint_item(request: Request, drive_id: str, item_id: str) -> dict:
    """Import a file or folder from SharePoint with enhanced batch processing and error handling."""
    temp_dir = None
    try:
        temp_dir = Path(settings.TEMP_DIR)
        temp_dir.mkdir(parents=True, exist_ok=True)
        access_token = await get_microsoft_access_token(request)
        item = await app.state.sharepoint_client.get_drive_item(
            drive_id, item_id, token=access_token
        )
        if not item:
            raise ValueError("Item not found")
        progress = {
            "total_files": 0,
            "processed_files": 0,
            "failed_files": 0,
            "skipped_files": [],
            "errors": [],
        }

        async def process_single_file(file_item, parent_path=""):
            file_path_for_error = file_item.get("name", "Unknown File")
            try:
                # --- Check if document already exists in index ---
                if (
                    hasattr(app.state, "index")
                    and app.state.index
                    and hasattr(app.state.index, "docstore")
                ):
                    existing_doc_id = None
                    target_sharepoint_id = file_item.get("id")
                    if target_sharepoint_id:
                        # Efficiently check if sharepoint_id exists in metadata
                        for doc_id, doc_info in app.state.index.docstore.docs.items():
                            if (
                                doc_info.metadata
                                and doc_info.metadata.get("sharepoint_id")
                                == target_sharepoint_id
                            ):
                                existing_doc_id = doc_id
                                break  # Found it

                    if existing_doc_id:
                        file_name_for_log = file_item.get("name", "Unknown File")
                        logger.info(
                            f"Skipping import for already indexed file: {file_name_for_log} (SharePoint ID: {target_sharepoint_id})"
                        )
                        progress["skipped_files"].append(
                            f"{file_item.get('name', 'Unknown File')} (already indexed)"
                        )
                        # Need to increment total_files here if we count skipped-as-existing towards total potential
                        # progress["total_files"] += 1 # Decide if this is desired
                        return  # Skip processing this file
                # --- End check ---

                file_name = file_item.get("name", "")
                file_path = parent_path + "/" + file_name if parent_path else file_name
                file_path_for_error = file_path
                file_size = file_item.get("size", 0)
                file_ext = Path(file_name).suffix.lower()

                # Check file size limitation
                if file_size > 100 * 1024 * 1024:
                    progress["skipped_files"].append(f"{file_path} (too large)")
                    return

                # Check supported file types
                if file_ext not in [
                    ".txt",
                    ".pdf",
                    ".doc",
                    ".docx",
                    ".rtf",
                    ".csv",
                    ".xlsx",
                    ".xls",
                    ".png",
                    ".jpg",
                    ".jpeg",
                    ".gif",
                    ".bmp",
                ]:
                    progress["skipped_files"].append(f"{file_path} (unsupported type)")
                    return

                # Download the file
                temp_file_path = temp_dir / file_name
                await app.state.sharepoint_client.download_file(
                    drive_id, file_item["id"], str(temp_file_path), access_token
                )

                # Special handling for specific problematic file
                if "DDB_BrandGuidelines-2025.pdf" in str(temp_file_path):
                    logger.warning(f"Detected problematic PDF file: {temp_file_path}")
                    try:
                        # Create a stub entry for the problematic PDF
                        metadata = {
                            "sharepoint_id": file_item["id"],
                            "created_datetime": file_item.get("createdDateTime"),
                            "last_modified_datetime": file_item.get(
                                "lastModifiedDateTime"
                            ),
                            "created_by": file_item.get("createdBy", {})
                            .get("user", {})
                            .get("displayName"),
                            "web_url": file_item.get("webUrl"),
                            "parent_path": parent_path,
                            "is_stub": True,
                        }

                        # Create a TextNode directly with a stub message
                        stub_text = f"DDB Brand Guidelines (2025) - This document contains design and branding guidelines for DDB. Please access the original document on SharePoint for complete details."
                        node = TextNode(
                            text=stub_text,
                            metadata={**metadata, "file_name": file_name},
                        )
                        app.state.index.insert_nodes([node])
                        await persist_index()
                        progress["processed_files"] += 1
                        logger.info(f"Successfully created stub entry for {file_name}")
                        temp_file_path.unlink()
                        return
                    except Exception as e:
                        logger.error(f"Error creating stub for {file_path}: {str(e)}")
                        progress["failed_files"] += 1
                        progress["errors"].append(f"Error with {file_path}: {str(e)}")
                        # Ensure we return here to stop processing this file on error
                        return

                # Standard processing for other files
                metadata = {
                    "sharepoint_id": file_item["id"],
                    "created_datetime": file_item.get("createdDateTime"),
                    "last_modified_datetime": file_item.get("lastModifiedDateTime"),
                    "created_by": file_item.get("createdBy", {})
                    .get("user", {})
                    .get("displayName"),
                    "web_url": file_item.get("webUrl"),
                    "parent_path": parent_path,
                }
                await add_document_to_index(str(temp_file_path), metadata)
                progress["processed_files"] += 1
                temp_file_path.unlink()
            except Exception as e:
                error_msg = f"Error processing {file_path_for_error}: {str(e)}"
                logger.error(error_msg)
                progress["failed_files"] += 1
                progress["errors"].append(error_msg)

        async def process_folder(folder_item, parent_path=""):
            folder_name_for_error = folder_item.get("name", "Unknown Folder")
            try:
                folder_name = folder_item.get("name", "")
                folder_name_for_error = (
                    f"{parent_path}/{folder_name}" if parent_path else folder_name
                )
                new_parent = (
                    parent_path + "/" + folder_name if parent_path else folder_name
                )
                logger.info(f"Processing folder: {new_parent}")
                async for (
                    child
                ) in app.state.sharepoint_client.list_folder_contents_recursive(
                    drive_id, folder_item["id"], token=access_token
                ):
                    logger.debug(
                        f"Processing child item: Name={child.get('name')}, Keys={list(child.keys())}, IsFolder={child.get('folder')}, IsFile={child.get('file')}"
                    )
                    if "folder" in child:
                        await process_folder(child, new_parent)
                    else:
                        progress["total_files"] += 1
                        await process_single_file(child, new_parent)
            except Exception as e:
                error_msg = f"Error processing folder {folder_name_for_error}: {str(e)}"
                logger.error(error_msg, exc_info=True)
                progress["errors"].append(error_msg)

        if "folder" in item:
            await process_folder(item)
        else:
            progress["total_files"] = 1
            await process_single_file(item)

        return {
            "status": "completed",
            "total_files": progress["total_files"],
            "processed_files": progress["processed_files"],
            "failed_files": progress["failed_files"],
            "skipped_files": progress["skipped_files"],
            "errors": progress["errors"],
        }

    except Exception as e:
        logger.error(f"Import failed: {str(e)}")
        return {"status": "failed", "error": str(e)}
    finally:
        if temp_dir and temp_dir.exists():
            shutil.rmtree(temp_dir)


@app.get(
    "/sharepoint/import/{drive_id}/{item_id}/{item_name}",
    name="import_sharepoint_item_route",
)
async def import_sharepoint_item_route(
    request: Request,
    drive_id: str,
    item_id: str,
    item_name: str,
    user=CurrentUser,
    ms_token: str = MsTokenDep,
):
    """
    Import a file or folder from SharePoint and render appropriate response.
    Requires Admin privileges.
    """
    # --- Admin Check ---
    session = request.session
    is_admin = False
    admin_emails_set = {
        email.strip().lower()
        for email in settings.ADMIN_EMAILS.split(",")
        if email.strip()
    }

    # Check 1: Basic Auth Admin User
    if (
        "basic_user" in session
        and session["basic_user"].get("authenticated")
        and session["basic_user"].get("username") == settings.USERNAME
    ):
        is_admin = True
        user_identifier = settings.USERNAME
        logger.debug(f"Admin access granted via Basic Auth user: {user_identifier}")

    # Check 2: Microsoft Logged-in User in Admin Emails List
    elif (
        "ms_user" in session
        and session["ms_user"].get("email")
        and session["ms_user"].get("email").lower() in admin_emails_set
    ):
        is_admin = True
        user_identifier = session["ms_user"]["email"]
        logger.debug(f"Admin access granted via Microsoft email: {user_identifier}")

    # If not admin by either method, raise Forbidden error
    if not is_admin:
        # Try to get identifier for logging purposes
        basic_user_info = session.get("basic_user", {})
        ms_user_info = session.get("ms_user", {})
        user_identifier = (
            basic_user_info.get("username")
            or ms_user_info.get("email")
            or "Unknown User"
        )

        logger.warning(
            f"Unauthorized attempt to access manual import by user: {user_identifier}"
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Manual SharePoint import is restricted to administrators.",
        )
    # --- End Admin Check ---

    try:
        # Log which admin user is performing the action
        logger.info(
            f"Admin user ({user_identifier}) initiated import for item: {item_name} (ID: {item_id}) from drive: {drive_id}"
        )
        # Call the import function, passing the request object
        result = await import_sharepoint_item(request, drive_id, item_id)

        if result["status"] == "completed":
            # Build success message
            message = f"Successfully processed {result['processed_files']} of {result['total_files']} files from '{item_name}'."
            details = []

            if result["skipped_files"]:
                details.append(f"Skipped files: {', '.join(result['skipped_files'])}")

            if result["failed_files"] > 0:
                details.append(f"Failed to process {result['failed_files']} files:")
                details.extend([f"  - {error}" for error in result["errors"]])

            return templates.TemplateResponse(
                "success.html",
                {
                    "request": request,
                    "message": message,
                    "details": "\n".join(details) if details else None,
                },
            )
        else:
            # Handle failed import
            return templates.TemplateResponse(
                "error.html",
                {
                    "request": request,
                    "error_title": "Import Failed",
                    "error_message": f"Failed to import '{item_name}'",
                    "error_details": result.get("error", "Unknown error occurred"),
                },
                status_code=500,
            )

    except Exception as e:
        logger.error(f"Error in import route: {str(e)}")
        return templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "error_title": "Import Error",
                "error_message": f"An unexpected error occurred while importing '{item_name}'",
                "error_details": str(e),
            },
            status_code=500,
        )


# --- RAG Query Endpoints ---
@app.get("/query")
async def query_endpoint(
    request: Request,
    query: str = Query(..., min_length=1, max_length=500),
    user=CurrentUser,
):
    """Query the RAG system with a natural language question."""
    # <<< Log session at query start >>>
    logger.debug(f"[/query] Session at start: {dict(request.session)}")
    # <<< End log >>>
    try:
        # Check if query engine is available and initialize if needed
        if not hasattr(app.state, "query_engine") or not app.state.query_engine:
            logger.warning("Query engine not available - attempting to initialize")

            # Ensure index is loaded
            if not hasattr(app.state, "index") or not app.state.index:
                try:
                    logger.info("Index not found, attempting to load...")
                    app.state.index = await load_index()
                    logger.info("Index loaded successfully")
                except Exception as e:
                    logger.error(f"Failed to load index: {e}")
                    return JSONResponse(
                        status_code=500,
                        content={
                            "error": "Failed to load vector index",
                            "details": str(e),
                        },
                    )

            # Create query engine from index
            try:
                app.state.query_engine = get_query_engine(
                    app.state.index, app.state.reranker
                )
                logger.info("Query engine successfully initialized on-demand")
            except Exception as model_error:
                logger.error(
                    f"Error creating query engine with current model: {model_error}"
                )

                # Try with a more reliable fallback model using isolated instances
                try:
                    logger.info(
                        "Attempting to create query engine with fallback model..."
                    )
                    # Create isolated fallback model instances
                    fallback_model_name = "gpt-3.5-turbo"
                    validate_model_name(fallback_model_name)
                    logger.info(f"Using validated fallback model: {fallback_model_name}")
                    
                    fallback_llm = LlamaOpenAI(
                        model=fallback_model_name,  # Fallback to a more widely available model
                        temperature=0.7,
                        max_tokens=1500,
                    )
                    fallback_embed_model = OpenAIEmbedding(model="text-embedding-3-small")

                    # Save original global settings
                    original_llm = getattr(LlamaSettings, 'llm', None)
                    original_embed_model = getattr(LlamaSettings, 'embed_model', None)
                    
                    try:
                        # Temporarily set fallback models for query engine creation
                        LlamaSettings.llm = fallback_llm
                        LlamaSettings.embed_model = fallback_embed_model

                        # Try again with fallback model
                        app.state.query_engine = get_query_engine(
                            app.state.index, app.state.reranker
                        )
                        logger.info("Successfully created query engine with fallback model")
                    finally:
                        # Restore original global settings to prevent contamination
                        if original_llm is not None:
                            LlamaSettings.llm = original_llm
                        elif hasattr(LlamaSettings, 'llm'):
                            delattr(LlamaSettings, 'llm')
                        
                        if original_embed_model is not None:
                            LlamaSettings.embed_model = original_embed_model
                        elif hasattr(LlamaSettings, 'embed_model'):
                            delattr(LlamaSettings, 'embed_model')
                except Exception as fallback_error:
                    logger.error(f"Fallback model also failed: {fallback_error}")
                    return JSONResponse(
                        status_code=500,
                        content={
                            "error": "Query engine initialization failed",
                            "details": f"Original error: {model_error}. Fallback error: {fallback_error}",
                        },
                    )

        # Verify query engine is actually initialized
        if not app.state.query_engine:
            logger.error(
                "Query engine still not available after initialization attempts"
            )
            return JSONResponse(
                status_code=500,
                content={
                    "error": "Unable to initialize query engine",
                    "details": "Please contact the administrator",
                },
            )

        # Process query and get result
        logger.info(f"Processing query: {query}")

        # Enhance the query to request more detailed responses
        enhanced_query = f"""Please provide a detailed and comprehensive answer to the following question. Include relevant examples, explanations, and context where appropriate. Break down complex concepts into understandable parts.

Question: {query}"""

        # Process query and get result - run sync query method in thread
        result = await asyncio.to_thread(app.state.query_engine.query, enhanced_query)
        logger.info(f"Query result type: {type(result)}")  # Log type for debugging

        # --- Log raw result structure ---
        logger.debug(f"Raw query result object: {result}")
        logger.debug(f"Raw query result source nodes: {result.source_nodes}")
        logger.debug(f"Raw query result metadata: {result.metadata}")
        # --- End log raw result structure ---

        # Assuming result is a Response object from LlamaIndex
        response_content = result.response  # Access the actual response string
        source_nodes = result.source_nodes  # Access source nodes
        
        # Apply relevance filtering and source limiting
        MINIMUM_RELEVANCE_THRESHOLD = 0.3  # 30% relevance threshold
        MAX_SOURCES_DEFAULT = 5  # Limit to top 5 sources by default
        
        # Track source counts for metadata
        total_sources = len(source_nodes) if source_nodes else 0
        relevant_sources_count = 0
        displayed_sources_count = 0
        
        # Filter out low-scoring sources and limit to top sources
        if source_nodes:
            # Filter by relevance score
            filtered_source_nodes = [
                node for node in source_nodes 
                if getattr(node, "score", 0) >= MINIMUM_RELEVANCE_THRESHOLD
            ]
            relevant_sources_count = len(filtered_source_nodes)
            # Limit to maximum number of sources
            source_nodes = filtered_source_nodes[:MAX_SOURCES_DEFAULT]
            displayed_sources_count = len(source_nodes)
            logger.info(f"Sources: {total_sources} total, {relevant_sources_count} relevant, showing {displayed_sources_count}")
        
        # Prepare follow-up questions (Example logic, adjust as needed)
        follow_up_questions = []
        # <<< Restore manual follow-up question generation >>>
        try:
            # Check if LLM is available on app state
            if hasattr(app.state, "llm") and app.state.llm:
                # Get source context information
                source_context = ""
                if source_nodes:
                    source_info = []
                    for node in source_nodes[:3]:  # Use top 3 sources
                        metadata = node.node.metadata if hasattr(node, "node") else node.metadata
                        if metadata and 'file_name' in metadata:
                            source_info.append(metadata['file_name'])
                    
                    if source_info:
                        source_context = f"\n\nThis answer was generated from these company documents: {', '.join(source_info)}. Focus on workplace policies, procedures, and company-related topics."

                prompt = f"""Based on the answer provided about '{query}' and the source documents used to generate it, suggest exactly 3 concise follow-up questions a user might ask to explore the topic further or dive deeper into the company policies and procedures. The questions should be relevant to both the answer content and the workplace/company context.{source_context}

Answer:
{response_content}

Output ONLY the questions, each on a new line, without any numbering or introduction."""

                # Use await directly as llm.acomplete should be async
                fu_resp = await app.state.llm.acomplete(prompt)

                # Parse response, removing potential numbering/bullets
                questions_raw = fu_resp.text.strip().split("\n")
                follow_up_questions = [
                    q.strip(" *-1234567890.) ")
                    for q in questions_raw
                    if q.strip()
                    and len(q.strip()) > 5  # Basic check for valid question
                ][:3]  # Take at most 3
                logger.info(f"Generated follow-up questions: {follow_up_questions}")
            else:
                logger.warning(
                    "LLM not available on app state, cannot generate follow-up questions."
                )
        except Exception as fu_err:
            logger.warning(
                f"Follow-up question generation failed: {fu_err}", exc_info=True
            )
        # <<< End restore >>>

        logger.info(
            f"Query response content: {response_content[:200]}..."
        )  # Log snippet

        return JSONResponse(
            status_code=200,
            content={
                "status": "completed",
                "response": response_content,  # Send the actual response string
                "source_nodes": [
                    {
                        # Use node.node to access the underlying TextNode if it's NodeWithScore
                        "text": node.node.text if hasattr(node, "node") else node.text,
                        "snippet": (
                            node.node.text[:200]
                            + ("…" if len(node.node.text) > 200 else "")
                        )
                        if hasattr(node, "node")
                        else (node.text[:200] + ("…" if len(node.text) > 200 else "")),
                        "score": getattr(node, "score", None),
                        "metadata": node.node.metadata
                        if hasattr(node, "node")
                        else node.metadata,
                    }
                    for node in source_nodes
                ],
                "follow_up_questions": follow_up_questions,
                "source_metadata": {
                    "total_sources": total_sources,
                    "relevant_sources": relevant_sources_count,
                    "displayed_sources": displayed_sources_count,
                    "has_more_sources": relevant_sources_count > displayed_sources_count
                },
            },
        )

    except Exception as e:
        logger.error(f"Error in query endpoint: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={
                "error": "An unexpected error occurred while processing the query",
                "details": str(e),
            },
        )


@app.get("/api/documents")
async def get_indexed_documents(request: Request, user=CurrentUser):
    """Returns metadata for documents currently in the vector index."""
    if not hasattr(app.state, "index") or not app.state.index:
        logger.error("Index not available for /api/documents")
        raise HTTPException(status_code=503, detail="Index is not ready.")

    docs_metadata = []
    try:
        # Access the document store associated with the index
        if hasattr(app.state.index, "docstore") and hasattr(
            app.state.index.docstore, "docs"
        ):
            # Iterate through documents in the docstore
            for doc_id, doc_info in app.state.index.docstore.docs.items():
                # Ensure metadata exists and add doc_id for reference
                # Filter out any potentially sensitive internal metadata if necessary
                metadata = doc_info.metadata or {}
                metadata["doc_id"] = doc_id  # Add the internal doc_id
                # Example: Select specific fields to return
                # filtered_metadata = {
                #     "doc_id": doc_id,
                #     "file_name": metadata.get("file_name"),
                #     "sharepoint_id": metadata.get("sharepoint_id"),
                #     "web_url": metadata.get("web_url"),
                #     "last_modified_datetime": metadata.get("last_modified_datetime")
                # }
                # docs_metadata.append(filtered_metadata)
                docs_metadata.append(metadata)  # Return all metadata for now
        else:
            logger.warning("Could not retrieve document metadata from index docstore.")
            # Return empty list if docstore structure is unexpected

        logger.info(f"Returning metadata for {len(docs_metadata)} indexed documents.")
        return JSONResponse(content={"documents": docs_metadata})

    except Exception as e:
        logger.error(f"Error retrieving indexed documents: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail="Failed to retrieve indexed documents metadata."
        )


@app.post("/api/documents/clear")
async def clear_indexed_documents(request: Request, user=CurrentUser):
    """Clears all documents from the vector index."""
    if not hasattr(app.state, "index") or not app.state.index:
        logger.error("Index not available for clearing documents")
        raise HTTPException(status_code=503, detail="Index is not ready.")

    try:
        # Create a new empty index
        app.state.index = await create_empty_index()
        await persist_index()
        logger.info("Successfully cleared all documents from index")
        return JSONResponse(content={"message": "All documents cleared successfully"})
    except Exception as e:
        logger.error(f"Error clearing documents: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail="Failed to clear documents from index."
        )


@app.get("/query/stream")
async def stream_query_endpoint(
    request: Request,
    query: str = Query(..., min_length=1, max_length=500),
    user=CurrentUser,
):
    """Stream the RAG system response with real-time chunks."""
    try:
        # Check if query engine is available and initialize if needed
        if not hasattr(app.state, "query_engine") or not app.state.query_engine:
            logger.warning("Query engine not available - attempting to initialize")

            # Ensure index is loaded
            if not hasattr(app.state, "index") or not app.state.index:
                try:
                    logger.info("Index not found, attempting to load...")
                    app.state.index = await load_index()
                    logger.info("Index loaded successfully")
                except Exception as e:
                    logger.error(f"Failed to load index: {e}")
                    def error_stream():
                        yield f"data: {json.dumps({'type': 'error', 'content': 'Failed to load vector index'})}\n\n"
                    return StreamingResponse(
                        error_stream(),
                        media_type="text/plain",
                        headers={"Cache-Control": "no-cache", "Connection": "keep-alive"}
                    )

            # Create query engine from index
            try:
                app.state.query_engine = get_query_engine(
                    app.state.index, app.state.reranker
                )
                logger.info("Query engine successfully initialized on-demand")
            except Exception as model_error:
                logger.error(f"Error creating query engine: {model_error}")
                def error_stream():
                    yield f"data: {json.dumps({'type': 'error', 'content': 'Query engine initialization failed'})}\n\n"
                return StreamingResponse(
                    error_stream(),
                    media_type="text/plain",
                    headers={"Cache-Control": "no-cache", "Connection": "keep-alive"}
                )

        # Verify query engine is actually initialized
        if not app.state.query_engine:
            logger.error("Query engine still not available after initialization attempts")
            def error_stream():
                yield f"data: {json.dumps({'type': 'error', 'content': 'Unable to initialize query engine'})}\n\n"
            return StreamingResponse(
                error_stream(),
                media_type="text/plain",
                headers={"Cache-Control": "no-cache", "Connection": "keep-alive"}
            )

        # Process query and get streaming result
        logger.info(f"Processing streaming query: {query}")

        # Enhance the query to request more detailed responses
        enhanced_query = f"""Please provide a detailed and comprehensive answer to the following question. Include relevant examples, explanations, and context where appropriate. Break down complex concepts into understandable parts.

Question: {query}"""

        def generate_stream():
            try:
                # Use streaming query method - this should be available with streaming=True
                streaming_response = app.state.query_engine.query(enhanced_query)

                # Check if response has streaming capability
                if hasattr(streaming_response, 'response_gen'):
                    # Stream the response chunks
                    for chunk in streaming_response.response_gen:
                        chunk_data = {
                            'type': 'chunk',
                            'content': str(chunk)
                        }
                        yield f"data: {json.dumps(chunk_data)}\n\n"
                else:
                    # Fallback to non-streaming if streaming not available
                    response_content = streaming_response.response
                    # Split response into chunks for pseudo-streaming
                    words = response_content.split(' ')
                    chunk_size = 3  # Send 3 words at a time
                    for i in range(0, len(words), chunk_size):
                        chunk = ' '.join(words[i:i+chunk_size])
                        if i + chunk_size < len(words):
                            chunk += ' '
                        chunk_data = {
                            'type': 'chunk',
                            'content': chunk
                        }
                        yield f"data: {json.dumps(chunk_data)}\n\n"

                # Generate follow-up questions
                follow_up_questions = []
                try:
                    if hasattr(app.state, "llm") and app.state.llm:
                        response_content = streaming_response.response if hasattr(streaming_response, 'response') else str(streaming_response)
                        
                        # Get source context information for streaming
                        source_context = ""
                        if hasattr(streaming_response, 'source_nodes') and streaming_response.source_nodes:
                            source_info = []
                            for node in streaming_response.source_nodes[:3]:  # Use top 3 sources
                                metadata = node.node.metadata if hasattr(node, "node") else node.metadata
                                if metadata and 'file_name' in metadata:
                                    source_info.append(metadata['file_name'])
                            
                            if source_info:
                                source_context = f"\n\nThis answer was generated from these company documents: {', '.join(source_info)}. Focus on workplace policies, procedures, and company-related topics."

                        prompt = f"""Based on the answer provided about '{query}' and the source documents used to generate it, suggest exactly 3 concise follow-up questions a user might ask to explore the topic further or dive deeper into the company policies and procedures. The questions should be relevant to both the answer content and the workplace/company context.{source_context}

Answer:
{response_content}

Output ONLY the questions, each on a new line, without any numbering or introduction."""

                        fu_resp = app.state.llm.complete(prompt)  # Use sync version in generator
                        questions_raw = fu_resp.text.strip().split("\n")
                        follow_up_questions = [
                            q.strip(" *-1234567890.) ")
                            for q in questions_raw
                            if q.strip() and len(q.strip()) > 5
                        ][:3]
                except Exception as fu_err:
                    logger.warning(f"Follow-up question generation failed: {fu_err}")

                # Apply relevance filtering and source limiting for streaming
                MINIMUM_RELEVANCE_THRESHOLD = 0.15  # 15% relevance threshold
                MAX_SOURCES_DEFAULT = 8  # Limit to top 8 sources by default
                
                # Track source counts for streaming metadata
                total_sources = 0
                relevant_sources_count = 0
                displayed_sources_count = 0
                filtered_source_nodes = []
                
                # Process source nodes with error handling
                try:
                    if hasattr(streaming_response, 'source_nodes') and streaming_response.source_nodes:
                        total_sources = len(streaming_response.source_nodes)
                        
                        # Filter by relevance score
                        relevant_nodes = [
                            node for node in streaming_response.source_nodes 
                            if getattr(node, "score", 0) >= MINIMUM_RELEVANCE_THRESHOLD
                        ]
                        relevant_sources_count = len(relevant_nodes)
                        
                        # Fallback: if no relevant sources, include top 2 sources regardless of threshold
                        if len(relevant_nodes) == 0 and streaming_response.source_nodes:
                            # Sort by score (highest first) and take top 2
                            sorted_nodes = sorted(
                                streaming_response.source_nodes, 
                                key=lambda n: getattr(n, "score", 0), 
                                reverse=True
                            )
                            relevant_nodes = sorted_nodes[:2]
                            logger.info(f"Fallback: No sources above {MINIMUM_RELEVANCE_THRESHOLD:.0%} threshold, including top {len(relevant_nodes)} sources")
                        
                        # Limit to maximum number of sources
                        filtered_source_nodes = relevant_nodes[:MAX_SOURCES_DEFAULT]
                        displayed_sources_count = len(filtered_source_nodes)
                        logger.info(f"Streaming - Sources: {total_sources} total, {relevant_sources_count} relevant, showing {displayed_sources_count}")
                except Exception as source_error:
                    logger.error(f"Error processing sources: {source_error}")
                    # Ensure we still have safe defaults
                    filtered_source_nodes = []
                    total_sources = 0
                    relevant_sources_count = 0
                    displayed_sources_count = 0
                
                # Send completion with metadata (with error handling for source processing)
                processed_source_nodes = []
                for node in filtered_source_nodes:
                    try:
                        node_text = node.node.text if hasattr(node, "node") else node.text
                        processed_source_nodes.append({
                            'text': node_text,
                            'snippet': (
                                node_text[:200] + ("…" if len(node_text) > 200 else "")
                            ) if node_text else "No content available",
                            'score': getattr(node, "score", None),
                            'metadata': node.node.metadata if hasattr(node, "node") else (node.metadata if hasattr(node, "metadata") else {}),
                        })
                    except Exception as node_error:
                        logger.warning(f"Error processing source node: {node_error}")
                        # Include a fallback node so frontend doesn't break
                        processed_source_nodes.append({
                            'text': "Content unavailable",
                            'snippet': "Error processing source content",
                            'score': None,
                            'metadata': {'file_name': 'Unknown Document', 'error': 'processing_failed'},
                        })
                
                completion_data = {
                    'type': 'complete',
                    'source_nodes': processed_source_nodes,
                    'follow_up_questions': follow_up_questions,
                    'source_metadata': {
                        'total_sources': total_sources,
                        'relevant_sources': relevant_sources_count,
                        'displayed_sources': displayed_sources_count,
                        'has_more_sources': relevant_sources_count > displayed_sources_count
                    },
                }
                yield f"data: {json.dumps(completion_data)}\n\n"

            except Exception as e:
                logger.error(f"Error in streaming query: {str(e)}")
                error_data = {
                    'type': 'error',
                    'content': f"An error occurred while processing the query: {str(e)}"
                }
                yield f"data: {json.dumps(error_data)}\n\n"

        return StreamingResponse(
            generate_stream(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "*",
            }
        )

    except Exception as e:
        logger.error(f"Error in streaming query endpoint: {str(e)}")
        def error_stream():
            error_data = {
                'type': 'error',
                'content': f"An unexpected error occurred: {str(e)}"
            }
            yield f"data: {json.dumps(error_data)}\n\n"

        return StreamingResponse(
            error_stream(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
            }
        )


# Health check endpoint
@app.api_route("/health", methods=["GET", "HEAD"])
async def health_check():
    """Endpoint to check if the service is alive and ready."""
    return JSONResponse(content={"status": "healthy"})


# Root endpoint to render the index page
@app.get("/")
async def root(request: Request):
    """Render the main index template for the application."""
    # Restore original logic
    is_admin = user_is_admin(request.session)
    logger.info(f"[/] Root page loaded. Admin status: {is_admin}")  # Simple log
    return templates.TemplateResponse(
        "index.html", {"request": request, "is_admin": is_admin}
    )


# --- Add missing /documents route ---#
@app.get("/documents", name="documents_page")
async def documents_page_redirect(request: Request, user: dict = CurrentUser):
    """Redirects to the main page. Exists to satisfy url_for calls."""
    logger.info("Redirecting from /documents to root (/)")
    return RedirectResponse(url="/", status_code=status.HTTP_302_FOUND)


# --- End add route ---


# --- Add user_is_admin helper function ---
def user_is_admin(session: dict) -> bool:
    """
    Returns True if the current session belongs to an admin user.
    Admin = basic-auth super-user  OR  Microsoft e-mail in ADMIN_EMAILS.
    """
    logger.info(f"[user_is_admin] Checking admin status for session: {session}")
    admin_emails_env = os.getenv("ADMIN_EMAILS", "")
    admin_emails_set = {
        e.strip().lower() for e in admin_emails_env.split(",") if e.strip()
    }
    logger.info(f"[user_is_admin] ADMIN_EMAILS from env: '{admin_emails_env}'")
    logger.info(f"[user_is_admin] Parsed admin_emails_set: {admin_emails_set}")
    basic_auth_admin_username = settings.USERNAME
    logger.info(
        f"[user_is_admin] Basic auth admin username from settings: '{basic_auth_admin_username}'"
    )

    # Check 1: Basic Auth Admin User
    basic_user_session = session.get("basic_user", {})
    basic_user_authenticated = basic_user_session.get("authenticated")
    basic_user_username = basic_user_session.get("username")
    logger.info(
        f"[user_is_admin] Basic user check: session_username='{basic_user_username}', is_authenticated={basic_user_authenticated}"
    )
    if basic_user_authenticated and basic_user_username == basic_auth_admin_username:
        logger.info("[user_is_admin] Result: True (Basic Auth Match)")
        return True

    # Check 2: Microsoft Logged-in User in Admin Emails List
    ms_user_session = session.get("ms_user", {})
    ms_user_email = ms_user_session.get("email", "").lower()
    logger.info(f"[user_is_admin] Microsoft user check: email='{ms_user_email}'")
    if ms_user_email in admin_emails_set:
        logger.info("[user_is_admin] Result: True (Microsoft Email Match)")
        return True

    logger.info("[user_is_admin] Result: False (No Match)")
    return False


# --- End add helper ---


# New endpoint for manual synchronization
@app.post("/sync/sharepoint/{drive_id}", name="sync_sharepoint_drive")
async def sync_sharepoint_drive(
    request: Request,
    drive_id: str,
    folder_path: str = Query(
        "",
        description="Optional subfolder path within the drive to sync. Default is root.",
    ),
    user=CurrentUser,
    ms_token: str = MsTokenDep,
):
    """Manually syncs the index with the current state of a SharePoint drive/folder."""
    logger.info(
        f"Starting manual sync for Drive ID: {drive_id}, Folder Path: '{folder_path or 'root'}'"
    )
    if not hasattr(app.state, "index") or not app.state.index:
        logger.error("Index not available for sync operation")
        raise HTTPException(status_code=503, detail="Index is not ready.")
    if not hasattr(app.state, "sharepoint_client"):
        logger.error("SharePoint client not available for sync operation")
        raise HTTPException(
            status_code=503, detail="SharePoint client is not configured or ready."
        )

    current_sp_ids = set()
    deleted_count = 0
    errors = []

    try:
        # 1. Get all current item IDs from SharePoint
        logger.info("Fetching current item IDs from SharePoint...")
        # Determine target folder ID (root or specific subfolder)
        target_item_id = "root"  # Default to root
        if folder_path:
            try:
                folder_item = await app.state.sharepoint_client.get_item_by_path(
                    drive_id, folder_path, token=ms_token
                )
                if folder_item and "id" in folder_item:
                    target_item_id = folder_item["id"]
                    logger.info(
                        f"Targeting folder '{folder_path}' with ID: {target_item_id}"
                    )
                else:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Folder path '{folder_path}' not found in drive {drive_id}.",
                    )
            except Exception as e:
                logger.error(
                    f"Error getting folder ID for path '{folder_path}': {e}",
                    exc_info=True,
                )
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to resolve folder path '{folder_path}'.",
                )

        # Recursively list files
        async for item in app.state.sharepoint_client.list_folder_contents_recursive(
            drive_id, target_item_id, token=ms_token
        ):
            if "file" in item:  # We only care about files for the index
                current_sp_ids.add(item.get("id"))

        logger.info(
            f"Found {len(current_sp_ids)} current file items in SharePoint target."
        )

        # 2. Get all sharepoint_ids from the index
        indexed_sp_ids = {}
        if hasattr(app.state.index, "docstore") and hasattr(
            app.state.index.docstore, "docs"
        ):
            for doc_id, doc_info in app.state.index.docstore.docs.items():
                sp_id = doc_info.metadata.get("sharepoint_id")
                if sp_id:
                    indexed_sp_ids[sp_id] = doc_id
        logger.info(
            f"Found {len(indexed_sp_ids)} items with sharepoint_id in the index."
        )

        # 3. Compare and find items to delete
        ids_in_index = set(indexed_sp_ids.keys())
        ids_to_delete = ids_in_index - current_sp_ids
        logger.info(f"Identified {len(ids_to_delete)} items to remove from the index.")

        # 4. Delete items from index
        if ids_to_delete:
            tasks = []
            for sp_id_to_delete in ids_to_delete:
                doc_id_to_delete = indexed_sp_ids[sp_id_to_delete]
                try:
                    # Run potentially blocking delete in thread
                    # Pass delete_from_docstore=True to remove from docstore as well
                    await asyncio.to_thread(
                        app.state.index.delete_ref_doc,
                        doc_id_to_delete,
                        delete_from_docstore=True,
                    )
                    logger.info(
                        f"Deleted doc_id {doc_id_to_delete} (SharePoint ID: {sp_id_to_delete}) from index."
                    )
                    deleted_count += 1
                except Exception as e:
                    error_msg = f"Failed to delete doc_id {doc_id_to_delete} (SharePoint ID: {sp_id_to_delete}): {e}"
                    logger.error(error_msg)
                    errors.append(error_msg)

            # 5. Persist index changes after deletions
            if deleted_count > 0:
                logger.info("Persisting index changes after deletions...")
                await persist_index()
                logger.info("Index persistence complete.")
            else:
                logger.info("No actual deletions performed, skipping persistence.")
        else:
            logger.info("No items to delete from the index.")

        return JSONResponse(
            content={
                "status": "sync_completed",
                "drive_id": drive_id,
                "folder_path": folder_path or "root",
                "sharepoint_items_found": len(current_sp_ids),
                "indexed_items_checked": len(indexed_sp_ids),
                "items_deleted_from_index": deleted_count,
                "errors": errors,
            }
        )

    except HTTPException as he:
        raise he  # Re-raise HTTP exceptions
    except Exception as e:
        logger.error(f"Error during SharePoint sync: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Sync failed: {str(e)}")
